import React from "react";

const ContactUsSection = () => {
  const contactCards = [
    {
      id: 1,
      title: "Customer Service",
      icon: (
        <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
          <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </div>
      ),
      phone: "**************",
      email: "<EMAIL>",
      description: "Available 24/7 for support"
    },
    {
      id: 2,
      title: "Finance & Accounting",
      icon: (
        <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
          <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
            <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
          </svg>
        </div>
      ),
      phone: "**************",
      email: "<EMAIL>",
      description: "Billing & payment inquiries"
    },
    {
      id: 3,
      title: "Management",
      icon: (
        <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
          <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
          </svg>
        </div>
      ),
      phone: "**************",
      email: "<EMAIL>",
      address: "411-225 Main Street, Toronto, Canada M5A 2Z3",
      description: "Executive & partnership inquiries"
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4" style={{ color: "#0D3166" }}>
            Contact Us
          </h2>
          <p className="text-gray-600">
            We're here to help with any questions you may have
          </p>
        </div>

        {/* Contact Cards Grid */}
        <div className="grid md:grid-cols-3 gap-8">
          {contactCards.map((card) => (
            <div
              key={card.id}
              className="bg-white rounded-lg p-6 shadow-sm border border-gray-100 text-center"
            >
              {/* Icon */}
              <div className="flex justify-center mb-4">
                {card.icon}
              </div>

              {/* Title */}
              <h3 className="text-lg font-semibold mb-4" style={{ color: "#0D3166" }}>
                {card.title}
              </h3>

              {/* Contact Info */}
              <div className="space-y-3 text-sm">
                {/* Phone */}
                <div className="flex items-center justify-center text-gray-600">
                  <svg className="w-4 h-4 mr-2" style={{ color: "#F52D2A" }} fill="currentColor" viewBox="0 0 24 24">
                    <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                  </svg>
                  {card.phone}
                </div>

                {/* Email */}
                <div className="flex items-center justify-center text-gray-600">
                  <svg className="w-4 h-4 mr-2" style={{ color: "#F52D2A" }} fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                  </svg>
                  {card.email}
                </div>

                {/* Address (only for Management) */}
                {card.address && (
                  <div className="flex items-start justify-center text-gray-600">
                    <svg className="w-4 h-4 mr-2 mt-0.5" style={{ color: "#F52D2A" }} fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                    </svg>
                    <span className="text-left">{card.address}</span>
                  </div>
                )}
              </div>

              {/* Description */}
              <p className="text-xs text-gray-500 mt-4">
                {card.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ContactUsSection;
