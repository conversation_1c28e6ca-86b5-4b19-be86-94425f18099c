import React, { useState } from "react";
import { UserWrapper } from "@/components/UserWrapper";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";
import ComplaintsDashboardModal from "@/components/ComplaintsDashboardModal";

interface DeliveryTask {
  id: number;
  type: string;
  price: string;
  postedTime: string;
  pickupLocation: string;
  dropoffLocation: string;
  estimatedDistance: string;
  deliveryDeadline: string;
}

const UserAvailableDeliveriesPage = () => {
  const [locationFilter, setLocationFilter] = useState("All Locations");
  const [packageTypeFilter, setPackageTypeFilter] = useState("All Types");
  const [deliveryDeadlineFilter, setDeliveryDeadlineFilter] =
    useState("Any Time");
  const [searchQuery, setSearchQuery] = useState("");
  const [showComplaintsModal, setShowComplaintsModal] = useState(false);

  // Mock data based on the image
  const deliveryTasks: DeliveryTask[] = [
    {
      id: 1,
      type: "Small Box",
      price: "15.00",
      postedTime: "1 hour ago",
      pickupLocation: "Toronto, ON",
      dropoffLocation: "North York, ON",
      estimatedDistance: "8.4 km",
      deliveryDeadline: "May 9, 6:45 PM (in 16.8 hrs)",
    },
    {
      id: 2,
      type: "Envelope",
      price: "10.50",
      postedTime: "3 hours ago",
      pickupLocation: "Vancouver, BC",
      dropoffLocation: "Burnaby, BC",
      estimatedDistance: "12.1 km",
      deliveryDeadline: "May 9, 8:15 PM (in 8.2 hrs)",
    },
    {
      id: 3,
      type: "Fragile Item",
      price: "25.00",
      postedTime: "5 hours ago",
      pickupLocation: "Montreal, QC",
      dropoffLocation: "Laval, QC",
      estimatedDistance: "15.7 km",
      deliveryDeadline: "May 9, 11:30 PM (in 5.5 hrs)",
    },
    {
      id: 4,
      type: "Small Box",
      price: "18.75",
      postedTime: "6 hours ago",
      pickupLocation: "Calgary, AB",
      dropoffLocation: "Airdrie, AB",
      estimatedDistance: "22.3 km",
      deliveryDeadline: "May 10, 2:15 AM (in 20.2 hrs)",
    },
    {
      id: 5,
      type: "Large Package",
      price: "30.00",
      postedTime: "8 hours ago",
      pickupLocation: "Ottawa, ON",
      dropoffLocation: "Kanata, ON",
      estimatedDistance: "19.5 km",
      deliveryDeadline: "May 10, 4:45 AM (in 22.7 hrs)",
    },
    {
      id: 6,
      type: "Envelope",
      price: "12.25",
      postedTime: "10 hours ago",
      pickupLocation: "Edmonton, AB",
      dropoffLocation: "St. Albert, AB",
      estimatedDistance: "14.2 km",
      deliveryDeadline: "May 10, 6:15 AM (in 24.2 hrs)",
    },
  ];

  const handleAcceptDelivery = (taskId: number) => {
    console.log(`Accepting delivery task ${taskId}`);
    // Handle accept delivery logic here
  };

  return (
    <UserWrapper>
      <div className="h-full bg-[#0F2C59] overflow-auto">
        <div className="p-8">
          {/* Header Section */}
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-white mb-6">
              Available Delivery Tasks
            </h1>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="bg-white rounded-lg p-6 text-center">
                <div className="flex items-center justify-center mb-3">
                  <span className="text-2xl mr-2">📦</span>
                </div>
                <div className="text-4xl font-bold text-gray-800 mb-1">124</div>
                <div className="text-gray-600 text-sm">
                  Deliveries Completed
                </div>
              </div>

              <div className="bg-white rounded-lg p-6 text-center">
                <div className="flex items-center justify-center mb-3">
                  <span className="text-2xl mr-2">⚠️</span>
                </div>
                <div className="text-4xl font-bold text-gray-800 mb-1">117</div>
                <div className="text-gray-600 text-sm">On-Time Deliveries</div>
              </div>

              <div className="bg-white rounded-lg p-6 text-center">
                <div className="flex items-center justify-center mb-3">
                  <span className="text-2xl mr-2">⚠️</span>
                </div>
                <div className="text-4xl font-bold text-gray-800 mb-1">3</div>
                <div className="text-gray-600 text-sm">Complaints Made</div>
              </div>
            </div>

            {/* View All Complaints Link */}
            <div className="mb-8 text-right">
              <button
                onClick={() => setShowComplaintsModal(true)}
                className="text-white hover:text-gray-300 text-sm font-medium"
              >
                View All Complaints →
              </button>
            </div>

            {/* Filters Section */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  Location
                </label>
                <select
                  value={locationFilter}
                  onChange={(e) => setLocationFilter(e.target.value)}
                  className="w-full px-3 py-2.5 bg-white border border-gray-300 rounded-md text-gray-900 text-sm focus:outline-none focus:ring-2 focus:ring-[#E63946] focus:border-[#E63946]"
                >
                  <option>All Locations</option>
                  <option>Toronto, ON</option>
                  <option>Vancouver, BC</option>
                  <option>Montreal, QC</option>
                  <option>Calgary, AB</option>
                  <option>Ottawa, ON</option>
                  <option>Edmonton, AB</option>
                </select>
              </div>

              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  Package Type
                </label>
                <select
                  value={packageTypeFilter}
                  onChange={(e) => setPackageTypeFilter(e.target.value)}
                  className="w-full px-3 py-2.5 bg-white border border-gray-300 rounded-md text-gray-900 text-sm focus:outline-none focus:ring-2 focus:ring-[#E63946] focus:border-[#E63946]"
                >
                  <option>All Types</option>
                  <option>Small Box</option>
                  <option>Envelope</option>
                  <option>Fragile Item</option>
                  <option>Large Package</option>
                </select>
              </div>

              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  Delivery Deadline
                </label>
                <select
                  value={deliveryDeadlineFilter}
                  onChange={(e) => setDeliveryDeadlineFilter(e.target.value)}
                  className="w-full px-3 py-2.5 bg-white border border-gray-300 rounded-md text-gray-900 text-sm focus:outline-none focus:ring-2 focus:ring-[#E63946] focus:border-[#E63946]"
                >
                  <option>Any Time</option>
                  <option>Within 6 hours</option>
                  <option>Within 12 hours</option>
                  <option>Within 24 hours</option>
                </select>
              </div>

              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  Search
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search deliveries..."
                    className="w-full px-3 py-2.5 pl-10 bg-white border border-gray-300 rounded-md text-gray-900 text-sm focus:outline-none focus:ring-2 focus:ring-[#E63946] focus:border-[#E63946]"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg
                      className="h-4 w-4 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Delivery Tasks Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {deliveryTasks.map((task) => (
                <div
                  key={task.id}
                  className="bg-white rounded-lg p-5 shadow-md"
                >
                  {/* Task Header */}
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800 mb-1">
                        {task.type}
                      </h3>
                      <p className="text-sm text-gray-600">
                        Posted {task.postedTime}
                      </p>
                    </div>
                    <div className="text-right">
                      <div
                        className="text-lg font-bold"
                        style={{ color: "#E63946" }}
                      >
                        eBa$ {task.price}
                      </div>
                      <div className="text-xs text-gray-600">On Time</div>
                    </div>
                  </div>

                  {/* Location Info */}
                  <div className="space-y-3 mb-5">
                    <div className="flex items-center">
                      <div className="w-4 h-4 mr-3 flex-shrink-0">
                        <svg
                          className="w-4 h-4 text-gray-500"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-700">
                          Pickup Location
                        </div>
                        <div className="text-sm text-gray-600">
                          {task.pickupLocation}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <div className="w-4 h-4 mr-3 flex-shrink-0">
                        <svg
                          className="w-4 h-4 text-gray-500"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-700">
                          Dropoff Location
                        </div>
                        <div className="text-sm text-gray-600">
                          {task.dropoffLocation}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <div className="w-4 h-4 mr-3 flex-shrink-0">
                        <svg
                          className="w-4 h-4 text-gray-500"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                          <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-700">
                          Estimated Distance
                        </div>
                        <div className="text-sm text-gray-600">
                          {task.estimatedDistance}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <div className="w-4 h-4 mr-3 flex-shrink-0">
                        <svg
                          className="w-4 h-4 text-gray-500"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-700">
                          Delivery Deadline
                        </div>
                        <div className="text-sm text-gray-600">
                          {task.deliveryDeadline}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Accept Button */}
                  <InteractiveButton
                    onClick={() => handleAcceptDelivery(task.id)}
                    className="w-full py-3 text-white font-medium rounded-md bg-[#0F2C59] hover:bg-[#0a1f3f]"
                  >
                    Accept Delivery
                  </InteractiveButton>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Complaints Dashboard Modal */}
      <ComplaintsDashboardModal
        isOpen={showComplaintsModal}
        onClose={() => setShowComplaintsModal(false)}
      />
    </UserWrapper>
  );
};

export default UserAvailableDeliveriesPage;
