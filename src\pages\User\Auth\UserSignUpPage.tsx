import * as yup from "yup";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { Link, useNavigate } from "react-router-dom";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum, RoleEnum } from "@/utils/Enums";
import { LazyLoad } from "@/components/LazyLoad";
import { useSDK } from "@/hooks/useSDK";
import { MkdPasswordInput } from "@/components/MkdPasswordInput";
import MkdInputV2 from "@/components/MkdInputV2";

interface UserSignUpPageProps {
  role?: string;
}

const UserSignUpPage = ({ role = RoleEnum.USER }: UserSignUpPageProps) => {
  const { sdk } = useSDK();
  const { authDispatch: dispatch, showToast } = useContexts();

  const [submitLoading, setSubmitLoading] = useState(false);
  const [referralType, setReferralType] = useState<"code" | "link" | "none">(
    "none"
  );
  const [termsAccepted, setTermsAccepted] = useState(false);

  const navigate = useNavigate();

  const schema = yup
    .object({
      fullName: yup.string().required("Full name is required"),
      email: yup.string().email("Invalid email").required("Email is required"),
      phoneNumber: yup.string().required("Phone number is required"),
      password: yup
        .string()
        .min(8, "Password must be at least 8 characters")
        .required("Password is required"),
      referralCode: yup.string().when("referralType", {
        is: "code",
        then: (schema) => schema.required("Referral code is required"),
        otherwise: (schema) => schema.notRequired(),
      }),
    })
    .required();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = async (data: yup.InferType<typeof schema>) => {
    if (!termsAccepted) {
      showToast(
        "Please accept the terms of service and privacy policy",
        4000,
        ToastStatusEnum.ERROR
      );
      return;
    }

    try {
      setSubmitLoading(true);
      // Here you would call your signup API
      // const result = await sdk.signup(data);

      console.log("Signup data:", data);
      showToast("Account created successfully!", 4000, ToastStatusEnum.SUCCESS);
      navigate("/user/login");
    } catch (error: any) {
      setSubmitLoading(false);
      showToast(
        error?.response?.data?.message || error?.message || "Signup failed",
        4000,
        ToastStatusEnum.ERROR
      );
    }
  };

  return (
    <main className="flex min-h-screen">
      {/* Left Sidebar */}
      <div className="w-[250px] bg-[#0F2C59] flex flex-col">
        {/* Logo */}
        <div className="p-6 border-b border-gray-600">
          <h1 className="text-2xl font-bold">
            <span style={{ color: "#E63946" }}>eBa</span>
            <span className="text-white">Dollar</span>
          </h1>
        </div>

        {/* Progress Steps */}
        <div className="p-6 flex-1">
          <div className="space-y-6">
            {/* Basic Information Step */}
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 rounded-full bg-[#E63946] flex items-center justify-center">
                <span className="text-white text-xs font-bold">1</span>
              </div>
              <div>
                <div className="text-white font-medium">Basic Information</div>
                <div className="text-gray-300 text-sm">50% Complete</div>
              </div>
            </div>

            {/* Verify Identity Step */}
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 rounded-full border-2 border-gray-500 flex items-center justify-center">
                <span className="text-gray-500 text-xs font-bold">2</span>
              </div>
              <div>
                <div className="text-gray-500 font-medium">Verify Identity</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Content Area */}
      <div className="flex-1 bg-white flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Create your account
            </h2>
            <p className="text-gray-600">
              Get started now and complete the remaining steps later.
            </p>
          </div>

          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-[#E63946]">
                  Basic Information
                </span>
                <span className="text-sm text-[#E63946]">50% Complete</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-[#E63946] h-2 rounded-full"
                  style={{ width: "50%" }}
                ></div>
              </div>
            </div>

            {/* Basic Info Section */}
            <div className="space-y-4">
              <div className="text-sm font-medium text-gray-700 mb-3">
                Basic Info
              </div>

              {/* Full Name */}
              <div>
                <LazyLoad>
                  <MkdInputV2
                    name="fullName"
                    type="text"
                    register={register}
                    errors={errors}
                    required
                    placeholder="Enter your full name"
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label className="text-sm font-medium text-gray-700">
                        Full Name*
                      </MkdInputV2.Label>
                      <MkdInputV2.Field className="!bg-white !border-gray-300 !text-gray-900 focus:!border-[#E63946] focus:!ring-[#E63946]" />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </LazyLoad>
              </div>

              {/* Email Address */}
              <div>
                <LazyLoad>
                  <MkdInputV2
                    name="email"
                    type="email"
                    register={register}
                    errors={errors}
                    required
                    placeholder="<EMAIL>"
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label className="text-sm font-medium text-gray-700">
                        Email Address*
                      </MkdInputV2.Label>
                      <MkdInputV2.Field className="!bg-white !border-gray-300 !text-gray-900 focus:!border-[#E63946] focus:!ring-[#E63946]" />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </LazyLoad>
              </div>

              {/* Phone Number */}
              <div>
                <LazyLoad>
                  <MkdInputV2
                    name="phoneNumber"
                    type="tel"
                    register={register}
                    errors={errors}
                    required
                    placeholder="(*************"
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label className="text-sm font-medium text-gray-700">
                        Phone Number*
                      </MkdInputV2.Label>
                      <MkdInputV2.Field className="!bg-white !border-gray-300 !text-gray-900 focus:!border-[#E63946] focus:!ring-[#E63946]" />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </LazyLoad>
              </div>

              {/* Password */}
              <div>
                <LazyLoad>
                  <MkdPasswordInput
                    required
                    name="password"
                    label="Password*"
                    errors={errors}
                    register={register}
                    inputClassName="!bg-white !border-gray-300 !text-gray-900 focus:!border-[#E63946] focus:!ring-[#E63946]"
                    placeholder="Create a strong password"
                    labelClassName="text-sm font-medium text-gray-700"
                  />
                </LazyLoad>
              </div>

              <p className="text-xs text-gray-500">
                Must be at least 8 characters with uppercase, lowercase and
                numbers
              </p>
            </div>

            {/* Referral Information */}
            <div className="bg-blue-50 rounded-lg p-4 space-y-4">
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">🎁</span>
                </div>
                <h3 className="text-sm font-medium text-gray-900">
                  Referral Information
                </h3>
              </div>

              {/* Referral Options */}
              <div className="space-y-3">
                {/* I have a referral code */}
                <div className="flex items-start space-x-3">
                  <input
                    type="radio"
                    id="referral-code"
                    name="referralType"
                    value="code"
                    checked={referralType === "code"}
                    onChange={(e) => setReferralType(e.target.value as "code")}
                    className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <label
                      htmlFor="referral-code"
                      className="text-sm font-medium text-gray-900 cursor-pointer"
                    >
                      I have a referral code
                    </label>
                    <p className="text-xs text-gray-600">
                      Enter the code shared by someone who referred you
                    </p>

                    {referralType === "code" && (
                      <div className="mt-2">
                        <LazyLoad>
                          <MkdInputV2
                            name="referralCode"
                            type="text"
                            register={register}
                            errors={errors}
                            placeholder="Enter referral code"
                          >
                            <MkdInputV2.Container>
                              <MkdInputV2.Field className="!bg-white !border-gray-300 !text-gray-900 focus:!border-[#E63946] focus:!ring-[#E63946]" />
                              <MkdInputV2.Error />
                            </MkdInputV2.Container>
                          </MkdInputV2>
                        </LazyLoad>
                      </div>
                    )}
                  </div>
                </div>

                {/* I clicked a referral link */}
                <div className="flex items-start space-x-3">
                  <input
                    type="radio"
                    id="referral-link"
                    name="referralType"
                    value="link"
                    checked={referralType === "link"}
                    onChange={(e) => setReferralType(e.target.value as "link")}
                    className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <label
                      htmlFor="referral-link"
                      className="text-sm font-medium text-gray-900 cursor-pointer"
                    >
                      I clicked a referral link
                    </label>
                    <p className="text-xs text-gray-600">
                      The referral information has been automatically detected
                    </p>

                    {referralType === "link" && (
                      <div className="mt-2 flex items-center space-x-2">
                        <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                        <span className="text-sm text-green-600">AppleBot</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* No referral */}
                <div className="flex items-start space-x-3">
                  <input
                    type="radio"
                    id="no-referral"
                    name="referralType"
                    value="none"
                    checked={referralType === "none"}
                    onChange={(e) => setReferralType(e.target.value as "none")}
                    className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <label
                      htmlFor="no-referral"
                      className="text-sm font-medium text-gray-900 cursor-pointer"
                    >
                      No referral
                    </label>
                    <p className="text-xs text-gray-600">
                      I found eBaDollar on my own
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-blue-100 rounded-md p-3">
                <div className="flex items-start space-x-2">
                  <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                    <span className="text-white text-xs">ℹ</span>
                  </div>
                  <p className="text-xs text-blue-800">
                    <strong>Referral Bonus:</strong> Both you and your referrer
                    will receive rewards when you complete your first
                    transaction!
                  </p>
                </div>
              </div>
            </div>

            {/* Terms and Conditions */}
            <div className="flex items-start space-x-3">
              <input
                type="checkbox"
                id="terms"
                checked={termsAccepted}
                onChange={(e) => setTermsAccepted(e.target.checked)}
                className="mt-1 w-4 h-4 text-[#E63946] border-gray-300 rounded focus:ring-[#E63946]"
              />
              <label
                htmlFor="terms"
                className="text-sm text-gray-700 cursor-pointer"
              >
                I have read and accepted the{" "}
                <Link to="/terms" className="text-[#E63946] hover:underline">
                  Terms of service
                </Link>{" "}
                and{" "}
                <Link to="/privacy" className="text-[#E63946] hover:underline">
                  Privacy policy
                </Link>
              </label>
            </div>

            {/* Verification Notice */}
            <div className="bg-blue-50 rounded-md p-3">
              <div className="flex items-start space-x-2">
                <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                  <span className="text-white text-xs">ℹ</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-blue-900">
                    Complete verification now or later
                  </p>
                  <p className="text-xs text-blue-700">
                    You can start using the application immediately after this
                    step. We will remind you to complete identity verification
                    later.
                  </p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                className="flex-1 px-4 py-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E63946]"
                onClick={() => navigate("/user/login")}
              >
                Cancel
              </button>

              <InteractiveButton
                type="submit"
                className="flex-1 !bg-[#E63946] hover:!bg-[#d63384] focus:!ring-[#E63946] !py-3 !font-medium"
                loading={submitLoading}
                disabled={submitLoading}
              >
                Create Account & Continue
              </InteractiveButton>
            </div>
          </form>

          {/* Login Link */}
          <p className="text-sm text-center text-gray-600 mt-6">
            Already have an account?{" "}
            <Link
              to="/user/login"
              className="font-medium text-[#E63946] hover:opacity-80"
            >
              Log In
            </Link>
          </p>
        </div>
      </div>
    </main>
  );
};

export default UserSignUpPage;
