import React, { useEffect, useState } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import { UserWrapper } from "../../../components/UserWrapper";
import InteractiveButton from "../../../components/InteractiveButton/InteractiveButton";
import { useSDK } from "../../../hooks/useSDK";
import { MkdLoader } from "../../../components/MkdLoader";
import { StarIcon } from "@/assets/svgs";

interface IListing {
  id: number;
  name: string;
  seller: string;
  seller_id?: number;
  price: string;
  status: string;
  description?: string;
  image?: string;
  category?: string;
  created_at: string;
  updated_at: string;
  rating?: number;
  sponsored?: boolean;
}

const UserMarketplaceDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [listing, setListing] = useState<IListing | null>(null);
  const [loading, setLoading] = useState(true);
  const [purchasing, setPurchasing] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [showMakeOfferModal, setShowMakeOfferModal] = useState(false);
  const [offerAmount, setOfferAmount] = useState("");
  const [offerMessage, setOfferMessage] = useState("");
  const { sdk } = useSDK();

  // Mock data - same as in UserMarketplaceListPage
  const mockListings = [
    {
      id: 1,
      name: "Premium Gaming Laptop - High Performance RTX 4080",
      seller: "Tech Paradise",
      price: "1299.99",
      status: "active",
      description:
        'This premium gaming laptop is the ultimate portable gaming machine, featuring the latest NVIDIA RTX graphics card and a blazing fast SSD processor to handle even the most demanding games.\n\nKey specifications:\n- Intel Core i9 processor 12th gen\n- NVIDIA RTX 4080 graphics\n- 32GB DDR5 RAM\n- 1TB NVMe SSD\n- 17.3" 4K display\n- RGB backlit keyboard\n- Windows 11 Pro\n\nThis laptop has been used for approximately 6 months and is in excellent condition. All original packaging, accessories, and warranty documentation included. Battery health at 95%.\n\nPerfect for:\n- High-end gaming\n- Content creation\n- Video editing\n- 3D rendering\n- Professional development\n\nAny questions? Feel free to message me for more details or to arrange a viewing. Serious buyers only please.',
      image: "/api/placeholder/600/400",
      category: "Electronics",
      created_at: "2024-01-15",
      updated_at: "2024-01-15",
      rating: 4.5,
      sponsored: true,
    },
    {
      id: 2,
      name: "Photography Services",
      seller: "PhotoMaster",
      price: "150.00",
      status: "active",
      description: "Professional photography for events and portraits",
      image: "/api/placeholder/600/400",
      category: "Services",
      created_at: "2024-01-14",
      updated_at: "2024-01-14",
      rating: 4.8,
      sponsored: true,
    },
    {
      id: 3,
      name: "Luxury Watch Collection",
      seller: "Luxury Finds",
      price: "3500.00",
      status: "active",
      description: "Collection of 3 premium watches, mint condition",
      image: "/api/placeholder/600/400",
      category: "Fashion",
      created_at: "2024-01-13",
      updated_at: "2024-01-13",
      rating: 4.2,
      sponsored: true,
    },
  ];

  // Mock product images for the gallery
  const productImages = [
    "/api/placeholder/600/400",
    "/api/placeholder/600/400",
    "/api/placeholder/600/400",
    "/api/placeholder/600/400",
    "/api/placeholder/600/400",
  ];

  useEffect(() => {
    if (id) {
      fetchListing();
    }
  }, [id]);

  const fetchListing = async () => {
    setLoading(true);
    try {
      // For now, using mock data - replace with actual API call
      // const result = await sdk.callRestAPI(
      //   {
      //     where: { id: parseInt(id!) },
      //   },
      //   "GET",
      //   "listing"
      // );

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      const foundListing = mockListings.find((l) => l.id === parseInt(id!));
      setListing(foundListing || null);
    } catch (error) {
      console.error("Error fetching listing:", error);
      setListing(null);
    } finally {
      setLoading(false);
    }
  };

  const handlePurchase = async () => {
    if (!listing) return;

    setPurchasing(true);
    try {
      // Create a transaction/order
      const result = await sdk.callRestAPI(
        {
          listing_id: listing.id,
          seller_id: listing.seller_id,
          amount: parseFloat(listing.price),
          status: "pending",
        },
        "POST"
      );

      if (result.error) {
        console.error("Error creating transaction:", result.message);
        alert("Failed to initiate purchase. Please try again.");
      } else {
        alert(
          "Purchase initiated successfully! Check your transactions for updates."
        );
        navigate("/user/transactions");
      }
    } catch (error) {
      console.error("Error creating transaction:", error);
      alert("Failed to initiate purchase. Please try again.");
    } finally {
      setPurchasing(false);
    }
  };

  const handleContactSeller = () => {
    // This would typically open a chat or messaging interface
    alert("Contact seller functionality would be implemented here");
  };

  const handleMakeOffer = () => {
    setShowMakeOfferModal(true);
  };

  const handleSubmitOffer = () => {
    // Handle offer submission logic here
    console.log("Offer submitted:", {
      amount: offerAmount,
      message: offerMessage,
    });
    alert(`Offer of eb$${offerAmount} submitted successfully!`);
    setShowMakeOfferModal(false);
    setOfferAmount("");
    setOfferMessage("");
  };

  const handleCancelOffer = () => {
    setShowMakeOfferModal(false);
    setOfferAmount("");
    setOfferMessage("");
  };

  const renderStars = (rating?: number) => {
    if (!rating) return null;
    return Array.from({ length: 5 }, (_, index) => (
      <StarIcon
        key={index}
        className={`w-4 h-4 ${
          index < Math.floor(rating) ? "text-yellow-400" : "text-gray-300"
        }`}
      />
    ));
  };

  const getSimilarListings = () => {
    return mockListings.filter((l) => l.id !== listing?.id).slice(0, 3);
  };

  if (loading) {
    return (
      <UserWrapper>
        <div className="flex justify-center items-center py-12">
          <MkdLoader />
        </div>
      </UserWrapper>
    );
  }

  if (!listing) {
    return (
      <UserWrapper>
        <div className="p-6 bg-[#001f3f] min-h-screen">
          <div className="text-center py-12">
            <div className="text-white text-lg mb-2">Listing not found</div>
            <InteractiveButton
              onClick={() => navigate("/user/marketplace")}
              className="bg-[#e53e3e] text-white px-4 py-2 rounded-md hover:bg-[#c53030]"
            >
              Back to Marketplace
            </InteractiveButton>
          </div>
        </div>
      </UserWrapper>
    );
  }

  return (
    <UserWrapper>
      <div className="bg-[#0F2C59] min-h-screen">
        {/* Header with breadcrumb */}
        <div className="px-6 py-4 border-b border-[#1a3a6b]">
          <div className="flex items-center text-white text-sm">
            <Link to="/user/marketplace" className="hover:text-gray-300">
              Listing Details
            </Link>
          </div>
        </div>

        <div className="flex">
          {/* Main Content */}
          <div className="flex-1 p-6">
            <div className="bg-white rounded-lg overflow-hidden">
              {/* Product Header */}
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">
                      {listing.name}
                    </h1>
                    <div className="flex items-center gap-2 mb-3">
                      {listing.sponsored && (
                        <span className="bg-[#E63946] text-white px-2 py-1 rounded text-xs font-medium">
                          Sponsored
                        </span>
                      )}
                      <div className="flex items-center gap-1">
                        {renderStars(listing.rating)}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-[#E63946] mb-1">
                      eb$ {listing.price}
                    </div>
                    <div className="text-sm text-gray-500">
                      Free shipping available
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-6">
                {/* Left Column - Images */}
                <div className="lg:col-span-2">
                  {/* Main Image */}
                  <div className="mb-4">
                    <div className="bg-gray-100 rounded-lg aspect-[4/3] flex items-center justify-center overflow-hidden">
                      <img
                        src={productImages[selectedImageIndex]}
                        alt={listing.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>

                  {/* Thumbnail Gallery */}
                  <div className="flex gap-2 mb-6">
                    {productImages.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setSelectedImageIndex(index)}
                        className={`w-16 h-16 rounded-lg overflow-hidden border-2 ${
                          selectedImageIndex === index
                            ? "border-[#E63946]"
                            : "border-gray-200"
                        }`}
                      >
                        <img
                          src={image}
                          alt={`Thumbnail ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>

                  {/* Description */}
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">
                      Description
                    </h3>
                    <div className="text-gray-700 text-sm leading-relaxed whitespace-pre-line">
                      {listing.description}
                    </div>
                  </div>

                  {/* User Rating */}
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">
                      User Rating
                    </h3>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1">
                        {renderStars(listing.rating)}
                      </div>
                      <span className="text-sm text-gray-600">
                        {listing.rating}/5 (24 reviews)
                      </span>
                    </div>
                  </div>
                </div>

                {/* Right Column - Purchase & Info */}
                <div className="space-y-6">
                  {/* Purchase Section */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="text-center mb-4">
                      <InteractiveButton
                        onClick={handlePurchase}
                        disabled={purchasing || listing.status !== "active"}
                        className="w-full bg-[#0F2C59] text-white py-3 px-6 rounded-md font-medium hover:bg-[#1a3a6b] transition-colors mb-3"
                      >
                        {purchasing ? "Processing..." : "Buy Now"}
                      </InteractiveButton>
                      <InteractiveButton
                        onClick={handleMakeOffer}
                        className="w-full border border-gray-300 text-gray-700 py-3 px-6 rounded-md font-medium hover:bg-gray-100 transition-colors"
                      >
                        Make Offer
                      </InteractiveButton>
                    </div>
                  </div>

                  {/* Seller Information */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">
                      Seller Information
                    </h3>
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {listing.seller}
                        </div>
                        <div className="text-sm text-gray-600">
                          Member since January 2024
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-1 mb-3">
                      {renderStars(4.8)}
                      <span className="text-sm text-gray-600 ml-1">
                        4.8/5 (156 reviews)
                      </span>
                    </div>
                    <div className="text-sm text-gray-600 mb-3">
                      Usually responds within 1 hour
                    </div>
                    <InteractiveButton
                      onClick={handleContactSeller}
                      className="w-full bg-white border border-gray-300 text-gray-700 py-2 px-4 rounded-md text-sm hover:bg-gray-50 transition-colors"
                    >
                      Contact Seller
                    </InteractiveButton>
                  </div>

                  {/* Safety Tips */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">
                      Safety Tips
                    </h3>
                    <ul className="text-sm text-gray-600 space-y-2">
                      <li className="flex items-start gap-2">
                        <span className="text-green-500 mt-1">✓</span>
                        Meet in person in a public place
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-green-500 mt-1">✓</span>
                        Check the item before you buy
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-green-500 mt-1">✓</span>
                        Pay only after collecting the item
                      </li>
                    </ul>
                    <Link
                      to="#"
                      className="text-[#0F2C59] text-sm hover:underline mt-3 inline-block"
                    >
                      Read more safety tips
                    </Link>
                  </div>
                </div>
              </div>

              {/* Delivery & Shipping */}
              <div className="border-t border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Delivery & Shipping
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 text-sm">✓</span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 text-sm">
                        Collect in Person
                      </div>
                      <div className="text-xs text-gray-600">
                        Free • Can be arranged for collection
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 text-sm">✓</span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 text-sm">
                        Deliver to Local Delivery
                      </div>
                      <div className="text-xs text-gray-600">
                        Free • Can be delivered to your location
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                      <span className="text-red-600 text-sm">✗</span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 text-sm">
                        Express & International Delivery
                      </div>
                      <div className="text-xs text-gray-600">
                        Not available • This seller doesn't support
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 text-sm">✓</span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 text-sm">
                        Courier Delivery
                      </div>
                      <div className="text-xs text-gray-600">
                        Available • Can be delivered via courier
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 text-sm">✓</span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 text-sm">
                        Collect & Send-up
                      </div>
                      <div className="text-xs text-gray-600">
                        Available • Can be collected and sent up
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Similar Listings */}
              <div className="border-t border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Similar Listings
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {getSimilarListings().map((similarListing) => (
                    <Link
                      key={similarListing.id}
                      to={`/user/marketplace/${similarListing.id}`}
                      className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                    >
                      <div className="aspect-[4/3] bg-gray-100">
                        <img
                          src={similarListing.image}
                          alt={similarListing.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="p-3">
                        <h4 className="font-medium text-gray-900 text-sm mb-1 line-clamp-2">
                          {similarListing.name}
                        </h4>
                        <div className="text-[#E63946] font-bold text-lg mb-1">
                          eb$ {similarListing.price}
                        </div>
                        <div className="flex items-center gap-1 mb-2">
                          {renderStars(similarListing.rating)}
                          <span className="text-xs text-gray-600 ml-1">
                            {similarListing.rating}/5
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
                          <span className="text-xs text-gray-600">
                            {similarListing.seller}
                          </span>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Make Offer Modal */}
        {showMakeOfferModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              {/* Modal Header */}
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Make an Offer for this Item
                </h3>
                <button
                  onClick={handleCancelOffer}
                  className="text-gray-400 hover:text-gray-600 text-xl"
                >
                  ×
                </button>
              </div>

              {/* Product Info */}
              <div className="flex items-center gap-3 mb-4 p-3 bg-gray-50 rounded-lg">
                <img
                  src={productImages[0]}
                  alt={listing?.name}
                  className="w-12 h-12 object-cover rounded"
                />
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 text-sm">
                    {listing?.name}
                  </h4>
                  <p className="text-[#E63946] font-bold text-sm">
                    eb$ {listing?.price}
                  </p>
                  <p className="text-xs text-gray-600">
                    Category: Electronics • Computers • Gaming Laptops
                  </p>
                </div>
              </div>

              {/* Offer Amount */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Your Offer (in eba$)
                  <span className="text-gray-400 ml-1">ⓘ</span>
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={offerAmount}
                    onChange={(e) => setOfferAmount(e.target.value)}
                    placeholder="1000.00"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                  />
                  <span className="absolute right-3 top-2 text-gray-500 text-sm">
                    eba$
                  </span>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Hint: Offer a price you'd like to pay. The seller can accept,
                  reject, or counter it.
                </p>
              </div>

              {/* Message */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Message (optional)
                </label>
                <textarea
                  value={offerMessage}
                  onChange={(e) => setOfferMessage(e.target.value)}
                  placeholder="Your message here..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent resize-none"
                />
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3">
                <button
                  onClick={handleCancelOffer}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSubmitOffer}
                  disabled={!offerAmount}
                  className="flex-1 px-4 py-2 bg-[#0F2C59] text-white rounded-md hover:bg-[#1a3a6b] transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  Make offer
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </UserWrapper>
  );
};

export default UserMarketplaceDetailPage;
