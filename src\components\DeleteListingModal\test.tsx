import React, { useState } from "react";
import DeleteListingModal from "./DeleteListingModal";

const DeleteListingModalTest: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Delete Listing Modal Test</h1>
      
      <button
        onClick={() => setIsOpen(true)}
        className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
      >
        Open Delete Modal
      </button>

      <DeleteListingModal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        itemName="Wireless Headphones"
        onConfirmDelete={() => {
          console.log("Delete confirmed!");
          setIsOpen(false);
        }}
      />
    </div>
  );
};

export default DeleteListingModalTest;
