import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { MkdInputV2 } from "@/components/MkdInputV2";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";

interface DeliveryPartnerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit?: () => void;
}

interface IDeliveryPartnerForm {
  full_name: string;
  email: string;
  phone_number: string;
  location: string;
  reference1_name: string;
  reference1_relationship: string;
  reference1_phone: string;
  reference1_email: string;
  reference2_name: string;
  reference2_relationship: string;
  reference2_phone: string;
  reference2_email: string;
  emergency_name: string;
  emergency_relationship: string;
  emergency_phone: string;
  vehicle_type: string;
  vehicle_make_model: string;
  license_plate: string;
  availability: string;
  documents_valid: boolean;
  terms_accepted: boolean;
}

const DeliveryPartnerModal: React.FC<DeliveryPartnerModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [vehiclePhotos, setVehiclePhotos] = useState({
    front: null,
    rear: null,
    left: null,
    right: null,
  });
  const [documents, setDocuments] = useState({
    license: null,
    insurance: null,
    government_id: null,
  });

  const schema = yup.object({
    full_name: yup.string().required("Full name is required"),
    email: yup.string().email("Invalid email").required("Email is required"),
    phone_number: yup.string().required("Phone number is required"),
    location: yup.string().required("Location is required"),
    reference1_name: yup.string().required("Reference 1 name is required"),
    reference1_relationship: yup.string().required("Relationship is required"),
    reference1_phone: yup.string().required("Phone number is required"),
    reference1_email: yup
      .string()
      .email("Invalid email")
      .required("Email is required"),
    reference2_name: yup.string().required("Reference 2 name is required"),
    reference2_relationship: yup.string().required("Relationship is required"),
    reference2_phone: yup.string().required("Phone number is required"),
    reference2_email: yup
      .string()
      .email("Invalid email")
      .required("Email is required"),
    emergency_name: yup.string().required("Emergency contact name is required"),
    emergency_relationship: yup.string().required("Relationship is required"),
    emergency_phone: yup.string().required("Phone number is required"),
    vehicle_type: yup.string().required("Vehicle type is required"),
    vehicle_make_model: yup
      .string()
      .required("Vehicle make and model is required"),
    license_plate: yup.string().required("License plate is required"),
    availability: yup.string().required("Availability is required"),
    documents_valid: yup
      .boolean()
      .oneOf([true], "You must confirm documents are valid"),
    terms_accepted: yup
      .boolean()
      .oneOf([true], "You must accept terms and conditions"),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<IDeliveryPartnerForm>({
    resolver: yupResolver(schema),
    defaultValues: {
      full_name: "Alex Johnson",
      email: "<EMAIL>",
      phone_number: "+****************",
      location: "New York, USA",
    },
  });

  const handleFormSubmit = async (data: IDeliveryPartnerForm) => {
    console.log("Form submitted:", data);
    // Handle form submission
    if (onSubmit) {
      onSubmit();
    } else {
      onClose();
    }
  };

  const handleFileUpload = (type: string, category: string) => {
    // Handle file upload logic
    console.log(`Upload ${type} for ${category}`);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-[#0F2C59]">
            Become an eBa Delivery Partner
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-xl font-bold"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="p-6">
          {/* Description */}
          <p className="text-gray-600 mb-6">
            Help users receive their purchases safely while earning with
            flexible delivery jobs.
          </p>

          {/* Your Information Section */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Your Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Full Name */}
              <div>
                <MkdInputV2
                  {...register("full_name")}
                  errors={errors.full_name?.message}
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label>Full Name*</MkdInputV2.Label>
                    <MkdInputV2.Field
                      placeholder="Alex Johnson"
                      className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                    />
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>

              {/* Email */}
              <div>
                <MkdInputV2
                  {...register("email")}
                  errors={errors.email?.message}
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label>Email*</MkdInputV2.Label>
                    <MkdInputV2.Field
                      placeholder="<EMAIL>"
                      className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                    />
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>

              {/* Phone Number */}
              <div>
                <MkdInputV2
                  {...register("phone_number")}
                  errors={errors.phone_number?.message}
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label>Phone Number*</MkdInputV2.Label>
                    <MkdInputV2.Field
                      placeholder="+****************"
                      className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                    />
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>

              {/* Location */}
              <div>
                <MkdInputV2
                  {...register("location")}
                  errors={errors.location?.message}
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label>Location*</MkdInputV2.Label>
                    <MkdInputV2.Field
                      placeholder="New York, USA"
                      className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                    />
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>
            </div>
          </div>

          {/* References Section */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              References *
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Reference 1 */}
              <div>
                <h4 className="text-base font-medium text-gray-700 mb-3">
                  Reference 1
                </h4>

                <div className="space-y-3">
                  <MkdInputV2
                    {...register("reference1_name")}
                    errors={errors.reference1_name?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Full Name*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="Reference full name"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>

                  <MkdInputV2
                    {...register("reference1_relationship")}
                    errors={errors.reference1_relationship?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Relationship*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="e.g. Supervisor, Colleague"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>

                  <MkdInputV2
                    {...register("reference1_phone")}
                    errors={errors.reference1_phone?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Phone Number*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="e.g. +****************"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>

                  <MkdInputV2
                    {...register("reference1_email")}
                    errors={errors.reference1_email?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Email*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="<EMAIL>"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>
              </div>

              {/* Reference 2 */}
              <div>
                <h4 className="text-base font-medium text-gray-700 mb-3">
                  Reference 2
                </h4>

                <div className="space-y-3">
                  <MkdInputV2
                    {...register("reference2_name")}
                    errors={errors.reference2_name?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Full Name*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="Reference full name"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>

                  <MkdInputV2
                    {...register("reference2_relationship")}
                    errors={errors.reference2_relationship?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Relationship*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="e.g. Supervisor, Colleague"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>

                  <MkdInputV2
                    {...register("reference2_phone")}
                    errors={errors.reference2_phone?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Phone Number*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="e.g. +****************"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>

                  <MkdInputV2
                    {...register("reference2_email")}
                    errors={errors.reference2_email?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Email*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="<EMAIL>"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>
              </div>
            </div>
          </div>

          {/* Emergency Contact Section */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Emergency Contact *
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <MkdInputV2
                {...register("emergency_name")}
                errors={errors.emergency_name?.message}
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Label>Full Name*</MkdInputV2.Label>
                  <MkdInputV2.Field
                    placeholder="Emergency contact name"
                    className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                  />
                  <MkdInputV2.Error />
                </MkdInputV2.Container>
              </MkdInputV2>

              <MkdInputV2
                {...register("emergency_relationship")}
                errors={errors.emergency_relationship?.message}
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Label>Relationship*</MkdInputV2.Label>
                  <MkdInputV2.Field
                    placeholder="e.g. Spouse, Parent"
                    className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                  />
                  <MkdInputV2.Error />
                </MkdInputV2.Container>
              </MkdInputV2>

              <MkdInputV2
                {...register("emergency_phone")}
                errors={errors.emergency_phone?.message}
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Label>Phone Number*</MkdInputV2.Label>
                  <MkdInputV2.Field
                    placeholder="e.g. +****************"
                    className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                  />
                  <MkdInputV2.Error />
                </MkdInputV2.Container>
              </MkdInputV2>
            </div>
          </div>

          {/* Vehicle & Legal Details Section */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Vehicle & Legal Details
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <MkdInputV2
                  {...register("vehicle_type")}
                  errors={errors.vehicle_type?.message}
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label>Vehicle Type*</MkdInputV2.Label>
                    <select
                      {...register("vehicle_type")}
                      className="w-full px-3 py-2 border border-[#D1D5DB] rounded-md bg-white text-black focus:outline-none focus:ring-2 focus:ring-[#0F2C59]"
                    >
                      <option value="">Select vehicle type</option>
                      <option value="car">Car</option>
                      <option value="motorcycle">Motorcycle</option>
                      <option value="bicycle">Bicycle</option>
                      <option value="van">Van</option>
                    </select>
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>

              <MkdInputV2
                {...register("vehicle_make_model")}
                errors={errors.vehicle_make_model?.message}
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Label>Vehicle Make & Model*</MkdInputV2.Label>
                  <MkdInputV2.Field
                    placeholder="e.g. Toyota Prius 2022"
                    className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                  />
                  <MkdInputV2.Error />
                </MkdInputV2.Container>
              </MkdInputV2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <MkdInputV2
                {...register("license_plate")}
                errors={errors.license_plate?.message}
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Label>License Plate Number*</MkdInputV2.Label>
                  <MkdInputV2.Field
                    placeholder="e.g. ABC-1234"
                    className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                  />
                  <MkdInputV2.Error />
                </MkdInputV2.Container>
              </MkdInputV2>

              <div>
                <MkdInputV2
                  {...register("availability")}
                  errors={errors.availability?.message}
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label>Availability*</MkdInputV2.Label>
                    <select
                      {...register("availability")}
                      className="w-full px-3 py-2 border border-[#D1D5DB] rounded-md bg-white text-black focus:outline-none focus:ring-2 focus:ring-[#0F2C59]"
                    >
                      <option value="">Select availability</option>
                      <option value="monday">Monday</option>
                      <option value="tuesday">Tuesday</option>
                      <option value="wednesday">Wednesday</option>
                      <option value="thursday">Thursday</option>
                      <option value="friday">Friday</option>
                      <option value="saturday">Saturday</option>
                      <option value="sunday">Sunday</option>
                      <option value="weekdays">Weekdays</option>
                      <option value="weekends">Weekends</option>
                      <option value="full-time">Full Time</option>
                    </select>
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
                <p className="text-xs text-gray-500 mt-1">
                  Hold Ctrl/Cmd to select multiple days
                </p>
              </div>
            </div>
          </div>

          {/* Vehicle Photos Section */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Vehicle Photos *
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Please upload clear photos of your vehicle from all sides for
              verification purposes
            </p>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[
                { key: "front", label: "Front View", icon: "🚗" },
                { key: "rear", label: "Rear View", icon: "🚗" },
                { key: "left", label: "Left Side", icon: "🚗" },
                { key: "right", label: "Right Side", icon: "🚗" },
              ].map((photo) => (
                <div key={photo.key} className="text-center">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 mb-2 hover:border-[#0F2C59] cursor-pointer">
                    <div className="text-2xl mb-2">{photo.icon}</div>
                    <p className="text-sm font-medium text-gray-700 mb-1">
                      {photo.label}
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={() => handleFileUpload(photo.key, "vehicle")}
                    className="text-sm text-[#0F2C59] hover:underline"
                  >
                    Upload
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Required Documents Section */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Required Documents
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[
                { key: "license", label: "Driver's License", icon: "🆔" },
                { key: "insurance", label: "Vehicle Insurance", icon: "📄" },
                {
                  key: "government_id",
                  label: "Government-issued ID",
                  icon: "📋",
                },
              ].map((doc) => (
                <div key={doc.key} className="text-center">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 mb-2 hover:border-[#0F2C59] cursor-pointer">
                    <div className="text-2xl mb-2">{doc.icon}</div>
                    <p className="text-sm font-medium text-gray-700 mb-1">
                      {doc.label} *
                    </p>
                    <p className="text-xs text-gray-500">
                      Click to upload or drag and drop
                      <br />
                      PDF, JPG or PNG (max 5MB)
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={() => handleFileUpload(doc.key, "document")}
                    className="text-sm text-[#0F2C59] hover:underline"
                  >
                    Browse Files
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Checkboxes */}
          <div className="mb-6 space-y-3">
            <label className="flex items-start">
              <input
                type="checkbox"
                {...register("documents_valid")}
                className="mt-1 mr-3 h-4 w-4 text-[#0F2C59] focus:ring-[#0F2C59] border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">
                I confirm that my documents are valid and up to date
              </span>
            </label>

            <label className="flex items-start">
              <input
                type="checkbox"
                {...register("terms_accepted")}
                className="mt-1 mr-3 h-4 w-4 text-[#0F2C59] focus:ring-[#0F2C59] border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">
                I agree to the{" "}
                <button
                  type="button"
                  className="text-[#0F2C59] hover:underline"
                >
                  Delivery Terms and Conditions
                </button>
              </span>
            </label>
          </div>

          {/* Info Box */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <div className="w-6 h-6 bg-[#0F2C59] rounded-full flex items-center justify-center mr-3 mt-0.5">
                <span className="text-white text-xs">ℹ</span>
              </div>
              <div className="text-sm text-gray-700">
                <strong>
                  After submitting your application, our team will review your
                  details.
                </strong>{" "}
                This process typically takes 2-3 business days. You'll receive
                an email notification once your application has been reviewed.
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <InteractiveButton
              type="button"
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 rounded-md"
            >
              Cancel
            </InteractiveButton>
            <InteractiveButton
              type="submit"
              className="px-6 py-2 bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white rounded-md"
            >
              Submit Application
            </InteractiveButton>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DeliveryPartnerModal;
