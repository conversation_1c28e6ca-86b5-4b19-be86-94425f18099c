import React, { useState } from "react";

interface PromoteListingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirmPromotion: (days: number) => void;
  isLoading?: boolean;
}

const PromoteListingModal: React.FC<PromoteListingModalProps> = ({
  isOpen,
  onClose,
  onConfirmPromotion,
  isLoading = false,
}) => {
  const [days, setDays] = useState<string>("");

  if (!isOpen) return null;

  const handleConfirm = () => {
    const numDays = parseInt(days);
    if (numDays > 0) {
      onConfirmPromotion(numDays);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-[400px] max-w-[90vw]">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
          <div className="flex items-center gap-3">
            {/* Promote Icon */}
            <div className="w-8 h-8 bg-[#0F2C59] rounded flex items-center justify-center">
              <svg
                className="w-4 h-4 text-white"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            </div>
            <h2 className="text-lg font-semibold text-[#0F2C59]">
              Promote Your Listing
            </h2>
          </div>

          {/* Close Button */}
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="px-6 py-5">
          {/* Question */}
          <p className="text-sm text-gray-700 mb-5">
            For how many days do you want to promote this listing?
          </p>

          {/* Input Field */}
          <div className="mb-5">
            <input
              type="number"
              value={days}
              onChange={(e) => setDays(e.target.value)}
              placeholder="Enter number of days"
              className="w-full px-3 py-2.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm placeholder-gray-400"
              min="1"
            />
          </div>

          {/* Promotion Rate */}
          <div className="flex items-center justify-between mb-6">
            <span className="text-sm text-gray-700 font-medium">
              Promotion Rate
            </span>
            <span className="text-lg font-semibold text-gray-900">USD$ —</span>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="flex-1 px-4 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirm}
              disabled={!days || parseInt(days) <= 0 || isLoading}
              className="flex-1 px-4 py-2.5 text-sm font-medium text-white bg-[#0F2C59] rounded-md hover:bg-[#0a1f3d] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0F2C59] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? "Promoting..." : "Confirm Promotion"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromoteListingModal;
