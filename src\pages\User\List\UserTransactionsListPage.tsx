import React, { useEffect, useState, useCallback } from "react";
import { UserWrapper } from "@/components/UserWrapper";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";
import { Link, useNavigate } from "react-router-dom";
import { useSDK } from "@/hooks/useSDK";
import { MkdLoader } from "@/components/MkdLoader";
import { PaginationBar } from "@/components/PaginationBar";
import { MkdInputV2 } from "@/components/MkdInputV2";

interface ITransaction {
  id: string;
  date: string;
  listing: string;
  item: string;
  service: string;
  type: string; // 'Sale' or 'Purchase'
  counterparty: string;
  amount: string;
  fee: string;
  net_received_paid: string;
  status: string;
  actions: string;
}

interface IPagination {
  page: number;
  limit: number;
  total: number;
  num_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

const UserTransactionsListPage = () => {
  const navigate = useNavigate();

  // Mock data to match the uploaded image exactly
  const mockTransactions: ITransaction[] = [
    {
      id: "1",
      date: "04/20",
      listing: "Wireless Headphones",
      item: "",
      service: "",
      type: "Sale",
      counterparty: "<PERSON>",
      amount: "eBa$ 129.9",
      fee: "eBa$ 6.5",
      net_received_paid: "eBa$ 123.49",
      status: "Completed",
      actions: "",
    },
    {
      id: "2",
      date: "04/18",
      listing: "Digital Camera",
      item: "",
      service: "",
      type: "Purchase",
      counterparty: "Camera World",
      amount: "eBa$ 349.5",
      fee: "$3.49",
      net_received_paid: "eBa$ 349.50",
      status: "Completed",
      actions: "",
    },
    {
      id: "3",
      date: "04/15",
      listing: "Graphic Design Services",
      item: "",
      service: "",
      type: "Sale",
      counterparty: "Sarah Johnson",
      amount: "eBa$ 129.9",
      fee: "eBa$ 6.5",
      net_received_paid: "eBa$ 123.49",
      status: "In Dispute",
      actions: "",
    },
    {
      id: "4",
      date: "04/12",
      listing: "Vintage Lamp",
      item: "",
      service: "",
      type: "Purchase",
      counterparty: "Antique Treasures",
      amount: "eBa$ 349.5",
      fee: "$3.49",
      net_received_paid: "eBa$ 349.50",
      status: "Refunded",
      actions: "",
    },
    {
      id: "5",
      date: "04/10",
      listing: "Smartphone Case",
      item: "",
      service: "",
      type: "Sale",
      counterparty: "David Lee",
      amount: "eBa$ 129.9",
      fee: "eBa$ 6.5",
      net_received_paid: "eBa$ 123.49",
      status: "Completed",
      actions: "",
    },
    {
      id: "6",
      date: "04/08",
      listing: "Cooking Class",
      item: "",
      service: "",
      type: "Purchase",
      counterparty: "Chef Maria's Kitchen",
      amount: "eBa$ 349.5",
      fee: "$3.49",
      net_received_paid: "eBa$ 349.50",
      status: "Completed",
      actions: "",
    },
  ];

  const [transactions, setTransactions] =
    useState<ITransaction[]>(mockTransactions);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<IPagination | null>({
    page: 1,
    limit: 6,
    total: 24,
    num_pages: 4,
    has_next: true,
    has_prev: false,
  });
  const [activeTab, setActiveTab] = useState("All");
  const [filters, setFilters] = useState({
    search: "",
    dateRange: "Last 30 days",
    transactionType: "All Transactions",
    status: "All Statuses",
  });

  const { sdk } = useSDK();

  const handlePageChange = (page: number) => {
    setPagination((prev) => (prev ? { ...prev, page } : null));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800";
      case "In Dispute":
        return "bg-yellow-100 text-yellow-800";
      case "Refunded":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <UserWrapper>
      <div className="p-6 bg-[#0F2C59] min-h-screen">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              Transaction History
            </h1>
            <p className="text-gray-300">
              View and manage your transaction history
            </p>
          </div>
          <button className="flex items-center px-4 py-2 bg-white text-gray-700 rounded-md hover:bg-gray-100">
            <span className="mr-2">📤</span>
            Export
          </button>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg p-6 mb-6">
          <div className="grid grid-cols-4 gap-4">
            <div>
              <input
                type="text"
                placeholder="Search by Item"
                value={filters.search}
                onChange={(e) =>
                  setFilters({ ...filters, search: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              />
            </div>
            <div>
              <select
                value={filters.dateRange}
                onChange={(e) =>
                  setFilters({ ...filters, dateRange: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option>Last 30 days</option>
                <option>Last 7 days</option>
                <option>Last 90 days</option>
              </select>
            </div>
            <div>
              <select
                value={filters.transactionType}
                onChange={(e) =>
                  setFilters({ ...filters, transactionType: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option>All Transactions</option>
                <option>Sales</option>
                <option>Purchases</option>
              </select>
            </div>
            <div>
              <select
                value={filters.status}
                onChange={(e) =>
                  setFilters({ ...filters, status: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option>All Statuses</option>
                <option>Completed</option>
                <option>In Dispute</option>
                <option>Refunded</option>
              </select>
            </div>
          </div>
          <div className="mt-4">
            <button className="px-6 py-2 bg-[#0F2C59] text-white rounded-md text-sm font-medium">
              Apply Filters
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg mb-6">
          <div className="flex border-b border-gray-200">
            {["All", "My Sales", "My Purchases"].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-6 py-3 text-sm font-medium border-b-2 ${
                  activeTab === tab
                    ? "border-[#E63946] text-[#E63946]"
                    : "border-transparent text-gray-500 hover:text-gray-700"
                }`}
              >
                {tab}
              </button>
            ))}
          </div>
        </div>

        {/* Table */}
        <div className="bg-white rounded-lg overflow-hidden mb-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Date
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Listing
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Item
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Service
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Type
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Counterparty
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Amount
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Fee
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Net Received/Paid
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {transactions.map((transaction) => (
                  <tr
                    key={transaction.id}
                    className="hover:bg-gray-50 cursor-pointer"
                    onClick={() =>
                      navigate(`/user/transactions/view/${transaction.id}`)
                    }
                  >
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {transaction.date}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {transaction.listing}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {transaction.item}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {transaction.service}
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full ${
                          transaction.type === "Sale"
                            ? "bg-blue-100 text-blue-800"
                            : "bg-purple-100 text-purple-800"
                        }`}
                      >
                        {transaction.type}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {transaction.counterparty}
                    </td>
                    <td className="px-4 py-3 text-sm font-medium text-gray-900">
                      {transaction.amount}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {transaction.fee}
                    </td>
                    <td className="px-4 py-3 text-sm font-medium text-gray-900">
                      {transaction.net_received_paid}
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(transaction.status)}`}
                      >
                        {transaction.status}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <button
                        className="text-gray-400 hover:text-gray-600 mr-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(`/user/transactions/view/${transaction.id}`);
                        }}
                      >
                        👁
                      </button>
                      <button
                        className="text-gray-400 hover:text-gray-600"
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle delete action
                        }}
                      >
                        🗑
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-3 gap-6 mb-6">
          <div className="bg-white rounded-lg p-6">
            <div className="text-sm text-gray-600 mb-1">Sales Summary</div>
            <div className="text-2xl font-bold text-gray-900 mb-1">
              eBa$ 1,250.48
            </div>
            <div className="text-sm text-green-600 flex items-center">
              <span className="mr-1">📈</span>
              12 transactions this month
            </div>
          </div>
          <div className="bg-white rounded-lg p-6">
            <div className="text-sm text-gray-600 mb-1">Purchases Summary</div>
            <div className="text-2xl font-bold text-gray-900 mb-1">
              eBa$ 845.25
            </div>
            <div className="text-sm text-red-600 flex items-center">
              <span className="mr-1">📉</span>8 transactions this month
            </div>
          </div>
          <div className="bg-white rounded-lg p-6">
            <div className="text-sm text-gray-600 mb-1">Total Fees Paid</div>
            <div className="text-2xl font-bold text-gray-900 mb-1">
              eBa$ 89.75
            </div>
            <div className="text-sm text-blue-600 flex items-center">
              <span className="mr-1">ℹ️</span>
              Average fee: eBa$ 4.49
            </div>
          </div>
        </div>

        {/* Pagination */}
        <div className="bg-white rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="text-sm text-gray-600">
              Showing 1 to 6 of 24 entries
            </div>
          </div>
          <div className="flex items-center justify-center">
            <nav className="flex items-center space-x-1">
              <button
                onClick={() => handlePageChange(pagination!.page - 1)}
                disabled={!pagination?.has_prev}
                className="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50"
              >
                Previous
              </button>
              {[1, 2, 3, 4].map((page) => (
                <button
                  key={page}
                  onClick={() => handlePageChange(page)}
                  className={`px-3 py-2 text-sm rounded ${
                    pagination?.page === page
                      ? "bg-[#E63946] text-white"
                      : "text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  {page}
                </button>
              ))}
              <button
                onClick={() => handlePageChange(pagination!.page + 1)}
                disabled={!pagination?.has_next}
                className="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50"
              >
                Next
              </button>
            </nav>
          </div>
        </div>
      </div>
    </UserWrapper>
  );
};

export default UserTransactionsListPage;
