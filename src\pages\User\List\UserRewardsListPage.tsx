import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
  Tooltip,
  ResponsiveContainer,
  CartesianGrid,
} from "recharts";
import { UserWrapper } from "@/components/UserWrapper";
import {
  ArrowTrendingUpIcon,
  Cog6ToothIcon,
  CreditCardIcon,
  DocumentDuplicateIcon,
  StarIcon,
  UsersIcon,
  ShoppingCartIcon,
  CalculatorIcon,
  ArrowDownTrayIcon,
  ChevronDownIcon,
} from "@heroicons/react/24/outline";
import { MkdInputV2 } from "@/components/MkdInputV2";

const StatCard = ({
  title,
  value,
  subtext,
}: {
  title: string;
  value: string;
  subtext: string;
}) => (
  <div>
    <p className="text-sm text-gray-500">{title}</p>
    <p className="text-2xl font-bold text-[#0F2C59]">{value}</p>
    <p className="text-xs text-gray-400">{subtext}</p>
  </div>
);

const chartData = {
  loyalty: [
    { name: "Nov", value: 250 },
    { name: "Dec", value: 350 },
    { name: "<PERSON>", value: 450 },
    { name: "Feb", value: 300 },
    { name: "<PERSON>", value: 370 },
    { name: "Apr", value: 420 },
  ],
  referral: [
    { name: "Nov", value: 280 },
    { name: "Dec", value: 220 },
    { name: "Jan", value: 400 },
    { name: "Feb", value: 320 },
    { name: "Mar", avalue: 430 },
    { name: "Apr", value: 400 },
  ],
  rewardsRedeemed: [
    { name: "Nov", value: 150 },
    { name: "Dec", value: 250 },
    { name: "Jan", value: 350 },
    { name: "Feb", value: 200 },
    { name: "Mar", value: 270 },
    { name: "Apr", value: 320 },
  ],
  commissionsRedeemed: [
    { name: "Nov", value: 200 },
    { name: "Dec", value: 180 },
    { name: "Jan", value: 300 },
    { name: "Feb", value: 240 },
    { name: "Mar", value: 330 },
    { name: "Apr", value: 300 },
  ],
};

const BarChartCard = ({
  title,
  data,
  color,
}: {
  title: string;
  data: any[];
  color: string;
}) => (
  <div className="bg-white p-4 rounded-lg shadow">
    <div className="flex justify-between items-center mb-4">
      <h3 className="font-semibold text-[#0F2C59]">{title}</h3>
      <p className="text-xs text-gray-400">Last 6 months</p>
    </div>
    <div className="h-48">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 5, right: 0, left: 0, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis
            dataKey="name"
            axisLine={false}
            tickLine={false}
            tick={{ fill: "#6B7280", fontSize: 12 }}
          />
          <YAxis hide={true} />
          <Tooltip
            cursor={{ fill: "rgba(15, 44, 89, 0.1)" }}
            contentStyle={{
              background: "#0F2C59",
              border: "none",
              borderRadius: "0.5rem",
              color: "white",
            }}
          />
          <Bar
            dataKey="value"
            fill={color}
            barSize={20}
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  </div>
);

const UserRewardsListPage = () => {
  const loyaltyData = {
    total: "eBa$ 1,245.80",
    thisMonth: "eBa$ 87.25",
    redeemed: "eBa$ 25.00",
    available: "eBa$ 458.30",
  };

  const performanceMetrics = {
    sales: "eBa$ 2,135.50",
    purchases: "eBa$ 1,250.75",
    creditScore: "725",
    starRating: 4.5,
  };

  const referralData = {
    code: "RYN123",
    link: "https://eba.com/signup?ref=RYN123",
    totalReferred: "12",
    activeThisMonth: "7 active this month",
    commissionsEarned: "eBa$ 320.15",
    commissionsThisMonth: "eBa$ 45.75 this month",
    signupBonus: "eBa$ 100.00",
    transactionCommission: "eBa$ 220.15",
  };

  const monthlyBreakdown = [
    {
      month: "Apr 2025",
      reward: "eBa$ 87.25",
      commission: "eBa$ 45.75",
      points: "eBa$ 133.00",
      loyaltyStatus: "Vesting",
      commissionStatus: "Paid",
    },
    {
      month: "Mar 2025",
      reward: "eBa$ 75.50",
      commission: "eBa$ 60.25",
      points: "eBa$ 135.75",
      loyaltyStatus: "Paid",
      commissionStatus: "Paid",
    },
    {
      month: "Feb 2025",
      reward: "eBa$ 62.30",
      commission: "eBa$ 35.00",
      points: "eBa$ 97.30",
      loyaltyStatus: "Paid",
      commissionStatus: "Paid",
    },
    {
      month: "Jan 2025",
      reward: "eBa$ 93.75",
      commission: "eBa$ 50.00",
      points: "eBa$ 143.75",
      loyaltyStatus: "Paid",
      commissionStatus: "Paid",
    },
    {
      month: "Dec 2024",
      reward: "eBa$ 65.45",
      commission: "eBa$ 25.00",
      points: "eBa$ 90.45",
      loyaltyStatus: "Paid",
      commissionStatus: "Paid",
    },
    {
      month: "Nov 2024",
      reward: "eBa$ 45.25",
      commission: "eBa$ 30.00",
      points: "eBa$ 75.25",
      loyaltyStatus: "Paid",
      commissionStatus: "Paid",
    },
  ];

  return (
    <UserWrapper>
      <div className="p-8 bg-[#F0F4F8] text-[#0F2C59]">
        <h1 className="text-4xl font-bold mb-8">Rewards Center</h1>

        {/* Loyalty Rewards Overview */}
        <div className="bg-white p-6 rounded-lg shadow mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Loyalty Rewards Overview</h2>
            <Cog6ToothIcon className="h-6 w-6 text-gray-400" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="Total Loyalty Rewards"
              value={loyaltyData.total}
              subtext="Lifetime earnings"
            />
            <StatCard
              title="This Month's Rewards"
              value={loyaltyData.thisMonth}
              subtext="April 2025"
            />
            <StatCard
              title="Redeemed This Month"
              value={loyaltyData.redeemed}
              subtext="April 2025"
            />
            <StatCard
              title="Available Balance"
              value={loyaltyData.available}
              subtext="After redeemed rewards"
            />
          </div>
        </div>

        {/* Rewards Calculator Preview */}
        <div className="bg-white p-6 rounded-lg shadow mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">
              Rewards Calculator Preview
            </h2>
            <DocumentDuplicateIcon className="h-6 w-6 text-gray-400" />
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <h3 className="font-semibold mb-4">Performance Metrics</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {/* Last Month Sales */}
                <div className="p-4 rounded-lg border border-gray-200">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm text-gray-500">Last Month Sales</p>
                      <p className="text-2xl font-bold text-[#0F2C59] my-1">
                        {performanceMetrics.sales}
                      </p>
                      <p className="text-xs font-medium text-green-500 flex items-center">
                        <ArrowTrendingUpIcon className="h-4 w-4 mr-1" />
                        +12% from previous month
                      </p>
                    </div>
                    <ArrowTrendingUpIcon className="h-6 w-6 text-green-400" />
                  </div>
                </div>

                {/* Last Month Purchases */}
                <div className="p-4 rounded-lg border border-gray-200">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm text-gray-500">
                        Last Month Purchases
                      </p>
                      <p className="text-2xl font-bold text-[#0F2C59] my-1">
                        {performanceMetrics.purchases}
                      </p>
                      <p className="text-xs font-medium text-green-500 flex items-center">
                        <ArrowTrendingUpIcon className="h-4 w-4 mr-1" />
                        +8% from previous month
                      </p>
                    </div>
                    <ShoppingCartIcon className="h-6 w-6 text-blue-400" />
                  </div>
                </div>

                {/* Credit Score */}
                <div className="p-4 rounded-lg border border-gray-200">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm text-gray-500">Credit Score</p>
                      <p className="text-2xl font-bold text-[#0F2C59] my-1">
                        {performanceMetrics.creditScore}
                      </p>
                      <span className="text-xs font-medium bg-purple-100 text-purple-700 px-2 py-0.5 rounded-full">
                        Excellent
                      </span>
                    </div>
                    <CreditCardIcon className="h-6 w-6 text-purple-400" />
                  </div>
                </div>

                {/* Star Rating */}
                <div className="p-4 rounded-lg border border-gray-200">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm text-gray-500">Star Rating</p>
                      <div className="flex items-center gap-2">
                        <p className="text-2xl font-bold text-[#0F2C59] my-1">
                          {performanceMetrics.starRating}
                        </p>
                        <div className="flex text-yellow-400">
                          {[...Array(5)].map((_, i) => (
                            <StarIcon
                              key={i}
                              className={`h-5 w-5 ${
                                i < Math.floor(performanceMetrics.starRating)
                                  ? "fill-current"
                                  : "stroke-current"
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                      <p className="text-xs text-gray-400">
                        Based on 247 reviews
                      </p>
                    </div>
                    <StarIcon className="h-6 w-6 text-yellow-400" />
                  </div>
                </div>
              </div>
            </div>

            {/* Reward Calculation */}
            <div>
              <h3 className="font-semibold mb-4">Reward Calculation</h3>
              <div className="bg-[#0F2C59] text-white p-6 rounded-lg flex flex-col items-center justify-center text-center h-full">
                <CalculatorIcon className="h-10 w-10 mb-4 text-yellow-400" />
                <p className="font-semibold text-white/90">Monthly Reward</p>
                <p className="text-4xl font-bold my-2">eBa$ 87.25</p>
              </div>
            </div>
          </div>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          <BarChartCard
            title="Loyalty Rewards"
            data={chartData.loyalty}
            color="#0F2C59"
          />
          <BarChartCard
            title="Referral Commissions"
            data={chartData.referral}
            color="#4A5568"
          />
          <BarChartCard
            title="Rewards Redeemed"
            data={chartData.rewardsRedeemed}
            color="#0F2C59"
          />
          <BarChartCard
            title="Commissions Redeemed"
            data={chartData.commissionsRedeemed}
            color="#4A5568"
          />
        </div>

        {/* Referral Program */}
        <div className="bg-white p-6 rounded-lg shadow mb-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Referral Program</h2>
            <UsersIcon className="h-6 w-6 text-gray-400" />
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
            <div>
              <div className="mb-4">
                <label className="text-sm font-medium text-gray-500">
                  Your Referral Code
                </label>
                <div className="relative mt-1">
                  <MkdInputV2 value={referralData.code} disabled>
                    <MkdInputV2.Field className="pr-10" />
                  </MkdInputV2>
                  <button className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <DocumentDuplicateIcon className="h-5 w-5 text-gray-400" />
                  </button>
                </div>
              </div>
              <div className="mb-6">
                <label className="text-sm font-medium text-gray-500">
                  Your Referral Link
                </label>
                <div className="relative mt-1">
                  <MkdInputV2 value={referralData.link} disabled>
                    <MkdInputV2.Field className="pr-10" />
                  </MkdInputV2>
                  <button className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <DocumentDuplicateIcon className="h-5 w-5 text-gray-400" />
                  </button>
                </div>
              </div>
              <div className="flex gap-4">
                <button className="bg-[#0F2C59] text-white px-6 py-2 rounded-lg font-semibold">
                  Share Link
                </button>
                <button className="border border-[#0F2C59] text-[#0F2C59] px-6 py-2 rounded-lg font-semibold">
                  Invite by Email
                </button>
              </div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg space-y-4">
              <div className="flex justify-between items-center">
                <p className="text-sm text-gray-500">Total Referred Users</p>
                <div>
                  <p className="font-bold text-right">
                    {referralData.totalReferred}
                  </p>
                  <p className="text-xs text-gray-400 text-right">
                    {referralData.activeThisMonth}
                  </p>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <p className="text-sm text-gray-500">Commissions Earned</p>
                <div>
                  <p className="font-bold text-right">
                    {referralData.commissionsEarned}
                  </p>
                  <p className="text-xs text-gray-400 text-right">
                    {referralData.commissionsThisMonth}
                  </p>
                </div>
              </div>
              <hr />
              <div>
                <p className="text-sm font-semibold mb-2">
                  Commission Breakdown
                </p>
                <div className="flex justify-between text-sm">
                  <p className="text-gray-500">Sign-up bonus (x4)</p>
                  <p className="font-medium">{referralData.signupBonus}</p>
                </div>
                <div className="flex justify-between text-sm">
                  <p className="text-gray-500">Transaction commission (2%)</p>
                  <p className="font-medium">
                    {referralData.transactionCommission}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Monthly Breakdown */}
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Monthly Breakdown</h2>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1 rounded border border-gray-300 px-3 py-1.5 text-sm">
                <span>2025</span>
                <ChevronDownIcon className="h-4 w-4 text-gray-500" />
              </div>
              <button className="p-2 rounded border border-gray-300">
                <ArrowDownTrayIcon className="h-5 w-5 text-gray-600" />
              </button>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead className="bg-gray-50/50">
                <tr className="text-left text-gray-500 font-medium">
                  <th className="py-2 px-3">Month</th>
                  <th className="py-2 px-3">Reward Earned</th>
                  <th className="py-2 px-3">Referral Commission</th>
                  <th className="py-2 px-3">Total Points</th>
                  <th className="py-2 px-3">Loyalty Status</th>
                  <th className="py-2 px-3">Commission Status</th>
                </tr>
              </thead>
              <tbody>
                {monthlyBreakdown.map((row, i) => (
                  <tr key={i} className="border-b border-gray-100">
                    <td className="py-3 px-3 font-semibold text-gray-700">
                      {row.month}
                    </td>
                    <td className="py-3 px-3">{row.reward}</td>
                    <td className="py-3 px-3">{row.commission}</td>
                    <td className="py-3 px-3">{row.points}</td>
                    <td className="py-3 px-3">
                      <span
                        className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          row.loyaltyStatus === "Paid"
                            ? "bg-green-100 text-green-800"
                            : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {row.loyaltyStatus}
                      </span>
                    </td>
                    <td className="py-3 px-3">
                      <span
                        className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          row.commissionStatus === "Paid"
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-700"
                        }`}
                      >
                        {row.commissionStatus}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </UserWrapper>
  );
};

export default UserRewardsListPage;
