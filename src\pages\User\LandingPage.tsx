import React from "react";
import LandingHeader from "./components/LandingHeader";
import HeroSection from "./components/HeroSection";
import HowItWorksSection from "./components/HowItWorksSection";
import WhyChooseSection from "./components/WhyChooseSection";
import EarnCommissionsSection from "./components/EarnCommissionsSection";
import MoreReasonsSection from "./components/MoreReasonsSection";
import EarnWithDeliverySection from "./components/EarnWithDeliverySection";
import KeyBenefitsSection from "./components/KeyBenefitsSection";
import FAQSection from "./components/FAQSection";
import WatchHowItWorksSection from "./components/WatchHowItWorksSection";
import UserTestimonialsSection from "./components/UserTestimonialsSection";
import AboutUsSection from "./components/AboutUsSection";
import ContactUsSection from "./components/ContactUsSection";
import CallToActionSection from "./components/CallToActionSection";
import FooterSection from "./components/FooterSection";
import ChatWidget from "./components/ChatWidget";

const UserLandingPage = () => {
  return (
    <div className="fixed inset-0 overflow-y-auto bg-white">
      <LandingHeader />
      <main>
        <HeroSection />
        <HowItWorksSection />
        <WhyChooseSection />
        <EarnCommissionsSection />
        <MoreReasonsSection />
        <EarnWithDeliverySection />
        <KeyBenefitsSection />
        <FAQSection />
        <WatchHowItWorksSection />
        <UserTestimonialsSection />
        <AboutUsSection />
        <ContactUsSection />
        <CallToActionSection />
        <FooterSection />
        <ChatWidget />
      </main>
    </div>
  );
};

export default UserLandingPage;
