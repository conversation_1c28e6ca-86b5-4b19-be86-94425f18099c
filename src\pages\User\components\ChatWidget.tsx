import React, { useState } from "react";

const ChatWidget = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState("");

  const handleChatClick = () => {
    setIsOpen(true);
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  const handleSendMessage = () => {
    if (message.trim()) {
      console.log("Message sent:", message);
      setMessage("");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      {/* Chat Modal */}
      {isOpen && (
        <div className="fixed bottom-6 right-6 z-50 w-80 bg-white rounded-lg shadow-2xl border border-gray-200">
          {/* Header */}
          <div
            className="flex items-center justify-between p-4 rounded-t-lg"
            style={{ backgroundColor: "#F52D2A" }}
          >
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              <div>
                <h3 className="text-white font-medium text-sm">
                  eBaDollar Assistant
                </h3>
                <p className="text-white text-xs opacity-90">Online now</p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-white hover:opacity-80 text-lg font-bold"
            >
              ×
            </button>
          </div>

          {/* Chat Content */}
          <div className="p-4 h-64 overflow-y-auto bg-gray-50">
            <div className="mb-4">
              <p className="text-gray-700 text-sm mb-3">
                Hi! I'm here to help. Here are some common questions:
              </p>

              <div className="space-y-2">
                <button className="block w-full text-left text-blue-600 text-sm hover:underline">
                  How do I sign up for eBaDollar?
                </button>
                <button className="block w-full text-left text-blue-600 text-sm hover:underline">
                  How does the referral program work?
                </button>
                <button className="block w-full text-left text-blue-600 text-sm hover:underline">
                  What is eBaCredit and how do I get it?
                </button>
                <button className="block w-full text-left text-blue-600 text-sm hover:underline">
                  How do I avoid exchange rate losses?
                </button>
              </div>
            </div>
          </div>

          {/* Message Input */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your message..."
                className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500"
              />
              <button
                onClick={handleSendMessage}
                className="p-2 rounded-md hover:opacity-90 transition-opacity duration-200"
                style={{ backgroundColor: "#F52D2A" }}
              >
                <svg
                  className="w-4 h-4 text-white"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Chat Button */}
      {!isOpen && (
        <div className="fixed bottom-6 right-6 z-50">
          <button
            onClick={handleChatClick}
            className="text-white text-sm font-medium px-4 py-3 rounded-full shadow-lg hover:opacity-90 transition-opacity duration-200 flex items-center space-x-2"
            style={{ backgroundColor: "#F52D2A" }}
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z" />
            </svg>
            <span>CLICK HERE TO CHAT</span>
          </button>
        </div>
      )}
    </>
  );
};

export default ChatWidget;
