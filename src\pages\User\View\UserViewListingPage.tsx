import React, { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate, Link } from "react-router-dom";
import { UserWrapper } from "../../../components/UserWrapper";
import InteractiveButton from "../../../components/InteractiveButton/InteractiveButton";
import { useSDK } from "../../../hooks/useSDK";
import { MkdLoader } from "../../../components/MkdLoader";
import { EditIcon } from "../../../assets/svgs";

interface IListing {
  id: number;
  name: string;
  price: string;
  status: string;
  description: string;
  image?: string;
  category?: string;
  created_at: string;
  updated_at: string;
}

const UserViewListingPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [listing, setListing] = useState<IListing | null>(null);
  const [loading, setLoading] = useState(true);
  const { sdk } = useSDK();

  useEffect(() => {
    if (id) {
      fetchListing();
    }
  }, [id]);

  const fetchListing = async () => {
    setLoading(true);
    try {
      const result = await sdk.callRestAPI(
        {
          where: { id: parseInt(id!) },
        },
        "GET"
      );

      if (result.error) {
        console.error("Error fetching listing:", result.message);
        setListing(null);
      } else {
        setListing(result.data || null);
      }
    } catch (error) {
      console.error("Error fetching listing:", error);
      setListing(null);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async () => {
    if (!listing) return;

    const newStatus = listing.status === "active" ? "draft" : "active";

    try {
      const result = await sdk.callRestAPI(
        {
          id: listing.id,
          status: newStatus,
        },
        "PUT"
      );

      if (result.error) {
        console.error("Error updating listing status:", result.message);
        alert("Failed to update listing status. Please try again.");
      } else {
        setListing((prev) => (prev ? { ...prev, status: newStatus } : null));
        alert(
          `Listing ${newStatus === "active" ? "activated" : "deactivated"} successfully!`
        );
      }
    } catch (error) {
      console.error("Error updating listing status:", error);
      alert("Failed to update listing status. Please try again.");
    }
  };

  const handleDelete = async () => {
    if (!listing) return;

    if (
      !confirm(
        "Are you sure you want to delete this listing? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      const result = await sdk.callRestAPI({ id: listing.id }, "DELETE");

      if (result.error) {
        console.error("Error deleting listing:", result.message);
        alert("Failed to delete listing. Please try again.");
      } else {
        alert("Listing deleted successfully!");
        navigate("/user/listings");
      }
    } catch (error) {
      console.error("Error deleting listing:", error);
      alert("Failed to delete listing. Please try again.");
    }
  };

  if (loading) {
    return (
      <UserWrapper>
        <div className="flex justify-center items-center py-12">
          <MkdLoader />
        </div>
      </UserWrapper>
    );
  }

  if (!listing) {
    return (
      <UserWrapper>
        <div className="p-6 bg-[#001f3f] min-h-screen">
          <div className="text-center py-12">
            <div className="text-white text-lg mb-2">Listing not found</div>
            <InteractiveButton
              onClick={() => navigate("/user/listings")}
              className="bg-[#e53e3e] text-white px-4 py-2 rounded-md hover:bg-[#c53030]"
            >
              Back to My Listings
            </InteractiveButton>
          </div>
        </div>
      </UserWrapper>
    );
  }

  return (
    <UserWrapper>
      <div className="p-6 bg-[#001f3f] min-h-screen">
        <div className="mb-6">
          <InteractiveButton
            onClick={() => navigate("/user/listings")}
            className="text-white hover:text-gray-300 mb-4"
          >
            ← Back to My Listings
          </InteractiveButton>
        </div>

        <div className="bg-white rounded-lg overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-6">
            {/* Image Section */}
            <div>
              <div className="h-96 bg-gray-200 rounded-lg flex items-center justify-center">
                {listing.image ? (
                  <img
                    src={listing.image}
                    alt={listing.name}
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <div className="text-gray-400 text-lg">
                    No Image Available
                  </div>
                )}
              </div>
            </div>

            {/* Details Section */}
            <div>
              <div className="mb-4">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  {listing.name}
                </h1>
                <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                  {listing.category && (
                    <span>Category: {listing.category}</span>
                  )}
                </div>
              </div>

              <div className="mb-6">
                <div className="text-4xl font-bold text-[#e53e3e] mb-4">
                  ${listing.price}
                </div>
                <div className="flex items-center gap-2 mb-4">
                  <span
                    className={`px-3 py-1 rounded-full text-sm font-medium ${
                      listing.status === "active"
                        ? "bg-green-100 text-green-800"
                        : listing.status === "sold"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {listing.status.charAt(0).toUpperCase() +
                      listing.status.slice(1)}
                  </span>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-2">Description</h3>
                <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                  {listing.description}
                </p>
              </div>

              <div className="space-y-3">
                <div className="flex gap-3">
                  <Link
                    to={`/user/listings/edit/${listing.id}`}
                    className="flex-1 bg-[#e53e3e] text-white py-3 px-6 rounded-md hover:bg-[#c53030] text-center flex items-center justify-center gap-2"
                  >
                    <EditIcon className="w-4 h-4" />
                    Edit Listing
                  </Link>

                  <InteractiveButton
                    onClick={handleToggleStatus}
                    className={`flex-1 py-3 px-6 rounded-md font-medium ${
                      listing.status === "active"
                        ? "bg-yellow-500 text-white hover:bg-yellow-600"
                        : "bg-green-500 text-white hover:bg-green-600"
                    }`}
                  >
                    {listing.status === "active" ? "Deactivate" : "Activate"}
                  </InteractiveButton>
                </div>

                <InteractiveButton
                  onClick={handleDelete}
                  className="w-full py-3 px-6 border border-red-500 text-red-500 rounded-md hover:bg-red-500 hover:text-white transition-colors"
                >
                  Delete Listing
                </InteractiveButton>

                {listing.status === "active" && (
                  <Link
                    to={`/user/marketplace/${listing.id}`}
                    className="block w-full py-3 px-6 border border-[#e53e3e] text-[#e53e3e] rounded-md hover:bg-[#e53e3e] hover:text-white transition-colors text-center"
                  >
                    View in Marketplace
                  </Link>
                )}
              </div>

              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="text-sm text-gray-500">
                  <div>
                    Created: {new Date(listing.created_at).toLocaleDateString()}
                  </div>
                  <div>
                    Updated: {new Date(listing.updated_at).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UserWrapper>
  );
};

export default UserViewListingPage;
