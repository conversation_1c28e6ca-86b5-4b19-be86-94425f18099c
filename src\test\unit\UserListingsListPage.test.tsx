import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import UserListingsListPage from '../../pages/User/List/UserListingsListPage';

// Mock the UserWrapper component
jest.mock('../../components/UserWrapper', () => {
  return {
    UserWrapper: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="user-wrapper">{children}</div>
    ),
  };
});

// Mock the MkdLoader component
jest.mock('../../components/MkdLoader', () => {
  return {
    MkdLoader: () => <div data-testid="loader">Loading...</div>,
  };
});

// Mock the useSDK hook
jest.mock('../../hooks/useSDK', () => ({
  useSDK: () => ({
    sdk: {
      request: jest.fn(),
    },
  }),
}));

// Mock react-router-dom hooks
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  Link: ({ children, to, ...props }: any) => (
    <a href={to} {...props}>
      {children}
    </a>
  ),
}));

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('UserListingsListPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders the page title correctly', () => {
    renderWithProviders(<UserListingsListPage />);
    expect(screen.getByText('My Listings')).toBeInTheDocument();
  });

  test('renders both tabs correctly', () => {
    renderWithProviders(<UserListingsListPage />);
    expect(screen.getByText('All Listings')).toBeInTheDocument();
    expect(screen.getByText('Sold & Action Needed')).toBeInTheDocument();
  });

  test('starts with All Listings tab active', () => {
    renderWithProviders(<UserListingsListPage />);
    const allListingsTab = screen.getByText('All Listings');
    expect(allListingsTab).toHaveClass('bg-[#E63946]');
  });

  test('switches to Sold & Action Needed tab when clicked', async () => {
    renderWithProviders(<UserListingsListPage />);
    const soldTab = screen.getByText('Sold & Action Needed');
    
    fireEvent.click(soldTab);
    
    await waitFor(() => {
      expect(soldTab).toHaveClass('bg-[#E63946]');
    });
  });

  test('shows different filter options for sold tab', async () => {
    renderWithProviders(<UserListingsListPage />);
    const soldTab = screen.getByText('Sold & Action Needed');
    
    fireEvent.click(soldTab);
    
    await waitFor(() => {
      expect(screen.getByText('Booked')).toBeInTheDocument();
      expect(screen.getByText('Waiting Confirmation')).toBeInTheDocument();
      expect(screen.getByText('Completed')).toBeInTheDocument();
    });
  });

  test('shows Sort By filter for sold tab', async () => {
    renderWithProviders(<UserListingsListPage />);
    const soldTab = screen.getByText('Sold & Action Needed');
    
    fireEvent.click(soldTab);
    
    await waitFor(() => {
      expect(screen.getByText('Sort By')).toBeInTheDocument();
      expect(screen.getByText('Newest Sold')).toBeInTheDocument();
    });
  });

  test('renders search input', () => {
    renderWithProviders(<UserListingsListPage />);
    expect(screen.getByPlaceholderText('Search by item name, category, or tag')).toBeInTheDocument();
  });

  test('renders filter and sort buttons', () => {
    renderWithProviders(<UserListingsListPage />);
    expect(screen.getByText('Filters')).toBeInTheDocument();
    expect(screen.getByText('Sort')).toBeInTheDocument();
  });

  test('renders Add New Listing button', () => {
    renderWithProviders(<UserListingsListPage />);
    expect(screen.getByText('+ Add New Listing')).toBeInTheDocument();
  });

  test('shows loading state initially', () => {
    renderWithProviders(<UserListingsListPage />);
    expect(screen.getByTestId('loader')).toBeInTheDocument();
  });

  test('renders currency converter widget', () => {
    renderWithProviders(<UserListingsListPage />);
    expect(screen.getByText('Currency Converter')).toBeInTheDocument();
  });

  test('renders inbox widget', () => {
    renderWithProviders(<UserListingsListPage />);
    expect(screen.getByText('Inbox 3')).toBeInTheDocument();
    expect(screen.getByText('Alex Johnson')).toBeInTheDocument();
  });

  test('search input updates on change', () => {
    renderWithProviders(<UserListingsListPage />);
    const searchInput = screen.getByPlaceholderText('Search by item name, category, or tag') as HTMLInputElement;
    fireEvent.change(searchInput, { target: { value: 'test search' } });
    expect(searchInput.value).toBe('test search');
  });

  test('filters toggle when filter button is clicked', () => {
    renderWithProviders(<UserListingsListPage />);
    const filtersButton = screen.getByText('Filters');
    
    // Initially filters should be visible
    expect(screen.getByText('Status')).toBeInTheDocument();
    
    // Click to hide filters
    fireEvent.click(filtersButton);
    
    // Click again to show filters
    fireEvent.click(filtersButton);
    expect(screen.getByText('Status')).toBeInTheDocument();
  });

  test('switches back to All Listings tab', async () => {
    renderWithProviders(<UserListingsListPage />);
    
    // First switch to sold tab
    const soldTab = screen.getByText('Sold & Action Needed');
    fireEvent.click(soldTab);
    
    // Then switch back to all listings
    const allListingsTab = screen.getByText('All Listings');
    fireEvent.click(allListingsTab);
    
    await waitFor(() => {
      expect(allListingsTab).toHaveClass('bg-[#E63946]');
      expect(screen.getByText('Date Added')).toBeInTheDocument(); // Should show Date Added instead of Sort By
    });
  });
});
