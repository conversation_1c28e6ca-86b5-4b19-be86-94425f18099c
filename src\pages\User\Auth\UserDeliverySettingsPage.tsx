import React, { useState } from "react";
import { UserWrapper } from "@/components/UserWrapper";

const UserDeliverySettingsPage = () => {
  const [deliveryActive, setDeliveryActive] = useState(true);
  const [selectedDays, setSelectedDays] = useState([
    "Monday 9 AM-5 PM",
    "Tuesday 9 AM-5 PM",
    "Wednesday 10 AM-6 PM",
    "Friday 9 AM-3 PM",
  ]);
  const [vehicleType, setVehicleType] = useState("Van");
  const [makeModel, setMakeModel] = useState("Toyota HiAce");
  const [licensePlate, setLicensePlate] = useState("ABC-1234");
  const [insuranceProvider, setInsuranceProvider] = useState("AllState");
  const [insuranceDocument, setInsuranceDocument] =
    useState("insurance_doc.pdf");
  const [expiryDate, setExpiryDate] = useState("Dec 15, 2025");
  const [governmentId, setGovernmentId] = useState("government_id.jpg");
  const [driversLicense, setDriversLicense] = useState("drivers_license.jpg");

  const weekDays = [
    "Monday 9 AM-5 PM",
    "Tuesday 9 AM-5 PM",
    "Wednesday 10 AM-6 PM",
    "Thursday 9 AM-5 PM",
    "Friday 9 AM-3 PM",
    "Saturday 10 AM-4 PM",
    "Sunday 12 PM-6 PM",
  ];

  const handleDayToggle = (day: string) => {
    setSelectedDays((prev) =>
      prev.includes(day) ? prev.filter((d) => d !== day) : [...prev, day]
    );
  };

  const removeDayTag = (day: string) => {
    setSelectedDays((prev) => prev.filter((d) => d !== day));
  };

  return (
    <UserWrapper>
      <div className="h-full bg-[#0F2C59] overflow-auto">
        <div className="p-6">
          {/* Header */}
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-white mb-2">
              Delivery Settings
            </h1>
          </div>

          <div className="space-y-6">
            {/* Delivery Status Section */}
            <div className="bg-white rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Delivery Status
              </h2>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-sm text-gray-700 mr-2">
                    Active for Deliveries
                  </span>
                  <span className="text-gray-400 text-sm">ⓘ</span>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={deliveryActive}
                    onChange={(e) => setDeliveryActive(e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#0F2C59]"></div>
                </label>
              </div>
            </div>

            {/* Weekly Availability Section */}
            <div className="bg-white rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Weekly Availability
              </h2>
              <div className="mb-4">
                <span className="text-sm text-gray-700 mb-3 block">
                  Availability
                </span>
                <div className="flex flex-wrap gap-2 mb-4">
                  {selectedDays.map((day) => (
                    <span
                      key={day}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#0F2C59] text-white"
                    >
                      {day}
                      <button
                        onClick={() => removeDayTag(day)}
                        className="ml-2 text-white hover:text-gray-300"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <select
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
                    defaultValue=""
                  >
                    <option value="" disabled>
                      Select day
                    </option>
                    {weekDays
                      .filter((day) => !selectedDays.includes(day))
                      .map((day) => (
                        <option key={day} value={day}>
                          {day.split(" ")[0]}
                        </option>
                      ))}
                  </select>

                  <select
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
                    defaultValue=""
                  >
                    <option value="" disabled>
                      Start time
                    </option>
                    <option value="9am">9:00 AM</option>
                    <option value="10am">10:00 AM</option>
                    <option value="11am">11:00 AM</option>
                    <option value="12pm">12:00 PM</option>
                  </select>

                  <select
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
                    defaultValue=""
                  >
                    <option value="" disabled>
                      End time
                    </option>
                    <option value="3pm">3:00 PM</option>
                    <option value="4pm">4:00 PM</option>
                    <option value="5pm">5:00 PM</option>
                    <option value="6pm">6:00 PM</option>
                  </select>
                </div>

                <button className="px-4 py-2 bg-[#0F2C59] text-white rounded-md text-sm font-medium hover:bg-[#1a3a6b]">
                  Add Time Slot
                </button>
              </div>
            </div>

            {/* Vehicle Information Section */}
            <div className="bg-white rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Vehicle Information
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Vehicle Type
                  </label>
                  <select
                    value={vehicleType}
                    onChange={(e) => setVehicleType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
                  >
                    <option value="Van">Van</option>
                    <option value="Car">Car</option>
                    <option value="Truck">Truck</option>
                    <option value="Motorcycle">Motorcycle</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Make & Model
                  </label>
                  <input
                    type="text"
                    value={makeModel}
                    onChange={(e) => setMakeModel(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    License Plate
                  </label>
                  <input
                    type="text"
                    value={licensePlate}
                    onChange={(e) => setLicensePlate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Insurance Provider
                  </label>
                  <input
                    type="text"
                    value={insuranceProvider}
                    onChange={(e) => setInsuranceProvider(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Insurance Document
                  </label>
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50">
                      <span className="text-gray-600 mr-2">📄</span>
                      <span className="text-gray-700">{insuranceDocument}</span>
                    </div>
                    <button className="text-gray-400 hover:text-gray-600">
                      👁
                    </button>
                    <button className="text-gray-400 hover:text-gray-600">
                      📥
                    </button>
                    <button className="text-gray-400 hover:text-gray-600">
                      🔗
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Expiry Date
                  </label>
                  <input
                    type="date"
                    value="2025-12-15"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>
              </div>
            </div>

            {/* Identity Verification Section */}
            <div className="bg-white rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Identity Verification
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Government ID
                  </label>
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50">
                      <span className="text-gray-600 mr-2">🆔</span>
                      <span className="text-gray-700">{governmentId}</span>
                    </div>
                    <button className="text-gray-400 hover:text-gray-600">
                      👁
                    </button>
                    <button className="text-gray-400 hover:text-gray-600">
                      🔗
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Driver's License
                  </label>
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50">
                      <span className="text-gray-600 mr-2">🪪</span>
                      <span className="text-gray-700">{driversLicense}</span>
                    </div>
                    <button className="text-gray-400 hover:text-gray-600">
                      👁
                    </button>
                    <button className="text-gray-400 hover:text-gray-600">
                      🔗
                    </button>
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <span className="text-sm text-gray-700">Status</span>
                <div className="flex items-center mt-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  <span className="text-sm text-green-600 font-medium">
                    Verified
                  </span>
                </div>
              </div>
            </div>

            {/* Admin Verification Info Section */}
            <div className="bg-white rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Admin Verification Info
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Application Status
                  </label>
                  <div className="text-sm text-gray-900 font-medium">
                    Approved
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Reviewed By
                  </label>
                  <div className="text-sm text-gray-900">
                    Admin Support Team
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Verified Date
                  </label>
                  <div className="text-sm text-gray-900">Apr 22, 2025</div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-4">
              <button className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50">
                Request Account Deactivation
              </button>

              <div className="flex space-x-3">
                <button className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50">
                  Cancel
                </button>
                <button className="px-6 py-2 bg-[#E63946] text-white rounded-md text-sm font-medium hover:bg-[#d63384]">
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UserWrapper>
  );
};

export default UserDeliverySettingsPage;
