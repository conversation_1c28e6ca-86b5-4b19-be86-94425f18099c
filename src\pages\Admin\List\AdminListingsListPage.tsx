import React, { useEffect, useState, useCallback } from "react";
import { AdminWrapper } from "../../../components/AdminWrapper";
import InteractiveButton from "../../../components/InteractiveButton/InteractiveButton";
import { Link } from "react-router-dom";
import { useSDK } from "../../../hooks/useSDK";
import { MkdLoader } from "../../../components/MkdLoader";
import { PaginationBar } from "../../../components/PaginationBar";
import emptyPageImg from "@/assets/images/empty-page.png";
import PromoteListingModal from "../../../components/PromoteListingModal/PromoteListingModal";
import { usePromoteListingMutation } from "../../../query/usePromotion";

interface IListing {
  id: number;
  name: string;
  seller: string;
  price: string;
  status: string;
}

interface IPagination {
  page: number;
  limit: number;
  total: number;
  num_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

const AdminListingsListPage = () => {
  const [listings, setListings] = useState<IListing[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<IPagination | null>(null);
  const [statusCounts, setStatusCounts] = useState({
    all: 0,
    active: 0,
    sold: 0,
    expired: 0,
    reported: 0,
  });
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    search: "",
    status: "",
  });
  const [searchInput, setSearchInput] = useState("");
  const [promoteModal, setPromoteModal] = useState<{
    isOpen: boolean;
    listingId: number | null;
  }>({
    isOpen: false,
    listingId: null,
  });

  const { sdk } = useSDK();
  const { mutate: promoteListingMutation, isPending: isPromoting } =
    usePromoteListingMutation();

  const fetchListings = async () => {
    setLoading(true);
    try {
      console.log("Fetching listings with filters:", filters);
      const result = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/admin/listings/${filters.page}/${filters.limit}`,
        method: "GET",
        params: {
          search: filters.search,
          status: filters.status,
          sort_by: "created_at",
          sort_order: "desc",
        },
      });

      console.log("Listings API response:", result);
      if (result) {
        setListings(result.data);
        setPagination((result as any).pagination);
        setStatusCounts((result as any).status_counts);
      }
    } catch (error) {
      console.error("Error fetching listings:", error);
    } finally {
      setLoading(false);
    }
  };

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setFilters((prev) => ({ ...prev, search: searchInput, page: 1 }));
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchInput]);

  useEffect(() => {
    fetchListings();
  }, [filters.page, filters.status, filters.limit, filters.search]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  const handleApplyFilters = () => {
    setFilters((prev) => ({ ...prev, search: searchInput, page: 1 }));
  };

  const handleStatusClick = (status: string) => {
    setFilters({ ...filters, status: status, page: 1 });
  };

  const handlePageChange = (page: number) => {
    setFilters((prev) => ({ ...prev, page }));
  };

  const handlePageSizeChange = (limit: number) => {
    setFilters((prev) => ({ ...prev, limit, page: 1 }));
  };

  const handlePromoteClick = (listingId: number) => {
    setPromoteModal({
      isOpen: true,
      listingId: listingId,
    });
  };

  const handlePromoteConfirm = async (days: number) => {
    if (!promoteModal.listingId) return;

    promoteListingMutation(
      {
        listing_id: promoteModal.listingId,
        days: days,
      },
      {
        onSuccess: () => {
          setPromoteModal({ isOpen: false, listingId: null });
          // Refresh listings to show updated status
          fetchListings();
        },
        onError: () => {
          // Error handling is done in the mutation hook
        },
      }
    );
  };

  const handlePromoteClose = () => {
    setPromoteModal({ isOpen: false, listingId: null });
  };

  return (
    <AdminWrapper>
      <div className="p-6 bg-[#F8F9FB] min-h-screen">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-[#1E293B]">
            Listings Management
          </h1>
          <p className="text-gray-500">
            Moderate and manage marketplace listings
          </p>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
          <div className="flex justify-between items-center mb-4">
            <div className="w-1/3 relative">
              <input
                type="text"
                placeholder="Search listings"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={searchInput}
                onChange={handleSearchChange}
                onKeyDown={(e) => e.key === "Enter" && handleApplyFilters()}
              />
              {filters.search && (
                <div className="absolute -bottom-6 left-0 text-xs text-blue-600">
                  Searching for: "{filters.search}"
                </div>
              )}
            </div>
            <InteractiveButton
              className="!bg-[#0F2C59] text-white"
              onClick={handleApplyFilters}
              disabled={loading}
            >
              {loading ? "Loading..." : "Apply filters"}
            </InteractiveButton>
          </div>
          <div className="flex space-x-2">
            <button
              className={`px-4 py-2 rounded-lg text-sm ${
                filters.status === ""
                  ? "bg-[#0F2C59] text-white"
                  : "bg-gray-200 text-gray-700"
              }`}
              onClick={() => handleStatusClick("")}
            >
              All ({statusCounts.all || 0})
            </button>
            <button
              className={`px-4 py-2 rounded-lg text-sm ${
                filters.status === "active"
                  ? "bg-[#0F2C59] text-white"
                  : "bg-gray-200 text-gray-700"
              }`}
              onClick={() => handleStatusClick("active")}
            >
              Active ({statusCounts.active || 0})
            </button>
            <button
              className={`px-4 py-2 rounded-lg text-sm ${
                filters.status === "sold"
                  ? "bg-[#0F2C59] text-white"
                  : "bg-gray-200 text-gray-700"
              }`}
              onClick={() => handleStatusClick("sold")}
            >
              Sold ({statusCounts.sold || 0})
            </button>
            <button
              className={`px-4 py-2 rounded-lg text-sm ${
                filters.status === "expired"
                  ? "bg-[#0F2C59] text-white"
                  : "bg-gray-200 text-gray-700"
              }`}
              onClick={() => handleStatusClick("expired")}
            >
              Expired ({statusCounts.expired || 0})
            </button>
            <button
              className={`px-4 py-2 rounded-lg text-sm ${
                filters.status === "reported"
                  ? "bg-[#0F2C59] text-white"
                  : "bg-gray-200 text-gray-700"
              }`}
              onClick={() => handleStatusClick("reported")}
            >
              Reported ({statusCounts.reported || 0})
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          {loading ? (
            <MkdLoader />
          ) : (
            <>
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="p-4 text-left text-sm font-semibold text-gray-600">
                      Listing Name
                    </th>
                    <th className="p-4 text-left text-sm font-semibold text-gray-600">
                      Seller
                    </th>
                    <th className="p-4 text-left text-sm font-semibold text-gray-600">
                      Price
                    </th>
                    <th className="p-4 text-left text-sm font-semibold text-gray-600">
                      Status
                    </th>
                    <th className="p-4 text-left text-sm font-semibold text-gray-600">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {listings.length === 0 ? (
                    <tr>
                      <td colSpan={5}>
                        <div className="flex flex-col items-center justify-center py-10">
                          <div className="mb-4 ">
                            <img
                              src={emptyPageImg}
                              alt="No records"
                              className="w-full h-full"
                            />
                          </div>
                          <div className="text-gray-500 text-lg font-medium">
                            No listings found.
                          </div>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    listings.map((listing) => (
                      <tr key={listing.id} className="border-b border-gray-200">
                        <td className="p-4 text-gray-800">{listing.name}</td>
                        <td className="p-4 text-gray-800">{listing.seller}</td>
                        <td className="p-4 text-gray-800 font-medium">
                          {listing.price}
                        </td>
                        <td className="p-4">
                          <span
                            className={`px-3 py-1 text-xs font-semibold rounded-full ${
                              listing.status.toLowerCase() === "active"
                                ? "text-green-700 bg-green-100"
                                : "text-gray-700 bg-gray-100"
                            }`}
                          >
                            {listing.status}
                          </span>
                        </td>
                        <td className="p-4">
                          <div className="flex gap-2">
                            <Link
                              to={`/admin/listings/view/${listing.id}`}
                              className="text-blue-600 hover:underline"
                            >
                              View
                            </Link>
                            <button
                              onClick={() => handlePromoteClick(listing.id)}
                              className="text-[#0F2C59] hover:underline font-medium"
                            >
                              Promote
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
              {pagination && pagination.total > 0 && (
                <PaginationBar
                  currentPage={pagination.page}
                  pageCount={pagination.num_pages}
                  pageSize={pagination.limit}
                  canPreviousPage={pagination.has_prev}
                  canNextPage={pagination.has_next}
                  updatePageSize={handlePageSizeChange}
                  updateCurrentPage={handlePageChange}
                  canChangeLimit={true}
                  startSize={50}
                  multiplier={10}
                />
              )}
            </>
          )}
        </div>
      </div>

      {/* Promote Listing Modal */}
      <PromoteListingModal
        isOpen={promoteModal.isOpen}
        onClose={handlePromoteClose}
        onConfirmPromotion={handlePromoteConfirm}
        isLoading={isPromoting}
      />
    </AdminWrapper>
  );
};

export default AdminListingsListPage;
