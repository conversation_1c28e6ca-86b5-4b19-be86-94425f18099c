import React, { useState } from "react";

interface FAQItem {
  question: string;
  answer: string;
}

const FAQSection = () => {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    setOpenItems((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  const faqData: FAQItem[] = [
    {
      question: "How do I avoid exchange rate losses with eBaDollar?",
      answer:
        "eBaDollar maintains a stable value that eliminates exchange rate volatility, protecting you from currency fluctuations.",
    },
    {
      question: "How does eBaDollar protect against inflation?",
      answer:
        "eBaDollar is designed with built-in mechanisms to maintain purchasing power and protect against inflationary pressures.",
    },
    {
      question: "How do I save up to 90% in cash on my purchases?",
      answer:
        "By using eBaDollar for transactions, you can access significant discounts and savings on participating merchant purchases.",
    },
    {
      question: "Why do I need to pay eBaCredit and transaction fees?",
      answer:
        "eBaCredit and transaction fees help maintain the platform's security, stability, and operational costs while keeping fees minimal.",
    },
    {
      question: "How do I advertise for free in eBaDollar?",
      answer:
        "eBaDollar offers free advertising opportunities for businesses and individuals within the platform ecosystem.",
    },
    {
      question: "What am I allowed to advertise on eBaDollar?",
      answer:
        "You can advertise legitimate products and services that comply with our community guidelines and terms of service.",
    },
    {
      question: "How do I get USD $500.00 in eBaCredit?",
      answer:
        "You can earn eBaCredit through various platform activities, referrals, and promotional offers available to users.",
    },
    {
      question: "How does my eBaCredit work?",
      answer:
        "eBaCredit functions as platform currency that can be used for transactions, services, and accessing premium features.",
    },
    {
      question: "How do I earn commissions on referrals?",
      answer:
        "Refer new users to the platform and earn commissions based on their activity and transactions within the system.",
    },
    {
      question: "Do I need a credit card to sign up?",
      answer:
        "No credit card is required for basic registration. You can start using eBaDollar with just your email and basic information.",
    },
    {
      question: "How does the eBaQuality work?",
      answer:
        "eBaQuality is our rating system that ensures transaction quality and builds trust between users in the marketplace.",
    },
    {
      question: "What makes eBaDollar safer than banks?",
      answer:
        "eBaDollar uses advanced security protocols and decentralized systems that provide enhanced protection compared to traditional banking.",
    },
    {
      question: "How do I sign up to do eBaDeliveries?",
      answer:
        "You can register as a delivery partner through our platform and start earning by delivering goods in your local area.",
    },
    {
      question: "How does eBaDollar help the world or the environment?",
      answer:
        "eBaDollar promotes sustainable commerce and reduces environmental impact through efficient local delivery networks.",
    },
    {
      question: "How do I cash in my eBaDollars?",
      answer:
        "You can convert your eBaDollars through our platform's exchange system or use them directly for purchases and services.",
    },
    {
      question: "Can eBaDollars be willed, transferred, or gifted?",
      answer:
        "Yes, eBaDollars can be transferred between users, gifted to others, and included in estate planning arrangements.",
    },
  ];

  const leftColumnItems = faqData.slice(0, 8);
  const rightColumnItems = faqData.slice(8, 16);

  const FAQItem = ({
    item,
    isOpen,
    onToggle,
  }: {
    item: FAQItem;
    isOpen: boolean;
    onToggle: () => void;
  }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-3">
      <button
        onClick={onToggle}
        className="w-full px-4 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
      >
        <span className="text-sm font-medium text-gray-800 pr-4">
          {item.question}
        </span>
        <svg
          className={`w-5 h-5 text-gray-500 transform transition-transform ${
            isOpen ? "rotate-180" : ""
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>
      {isOpen && (
        <div className="px-4 pb-4">
          <p className="text-sm text-gray-600 leading-relaxed">{item.answer}</p>
        </div>
      )}
    </div>
  );

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2
            className="text-3xl md:text-4xl font-bold"
            style={{ color: "#0D3166" }}
          >
            Frequently Asked Questions
          </h2>
        </div>

        {/* FAQ Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {/* Left Column */}
          <div>
            {leftColumnItems.map((item, index) => (
              <FAQItem
                key={index}
                item={item}
                isOpen={openItems.includes(index)}
                onToggle={() => toggleItem(index)}
              />
            ))}
          </div>

          {/* Right Column */}
          <div>
            {rightColumnItems.map((item, index) => {
              const actualIndex = index + 8;
              return (
                <FAQItem
                  key={actualIndex}
                  item={item}
                  isOpen={openItems.includes(actualIndex)}
                  onToggle={() => toggleItem(actualIndex)}
                />
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
