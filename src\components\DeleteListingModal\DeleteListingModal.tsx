import React from "react";

interface DeleteListingModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemName: string;
  onConfirmDelete: () => void;
}

const DeleteListingModal: React.FC<DeleteListingModalProps> = ({
  isOpen,
  onClose,
  itemName,
  onConfirmDelete,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-[400px] max-w-[90vw]">
        {/* Header with Icon */}
        <div className="flex flex-col items-center pt-8 pb-6 px-6">
          {/* Delete Icon */}
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <svg
              className="w-8 h-8 text-[#E63946]"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
          </div>

          {/* Title */}
          <h2 className="text-lg font-semibold text-[#0F2C59] mb-3">
            Delete Listing
          </h2>

          {/* Message */}
          <p className="text-sm text-gray-600 text-center mb-2">
            Are you sure you want to delete "{itemName}"?
          </p>

          {/* Warning Text */}
          <p className="text-xs text-gray-500 text-center leading-relaxed">
            This action cannot be undone. The listing will be
            <br />
            permanently removed from your account.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 px-6 pb-6">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            Cancel
          </button>
          <button
            onClick={onConfirmDelete}
            className="flex-1 px-4 py-2 text-sm font-medium text-white bg-[#E63946] rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E63946]"
          >
            Delete Listing
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteListingModal;
