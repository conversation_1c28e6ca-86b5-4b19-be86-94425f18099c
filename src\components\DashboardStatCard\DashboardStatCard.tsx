import React from "react";

interface DashboardStatCardProps {
  title: string;
  value: string;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  change?: string;
  changeColor?: string;
  subValue?: string;
}

const DashboardStatCard = ({
  title,
  value,
  icon,
  children,
  change,
  changeColor,
  subValue,
}: DashboardStatCardProps) => {
  return (
    <div className="flex items-center rounded-lg bg-white p-6 shadow">
      <div className="flex-1">
        <p className="text-sm text-gray-500">{title}</p>
        <p className="text-2xl font-bold">{value}</p>
        {change && (
          <p className={`text-sm ${changeColor || "text-gray-600"}`}>
            {change}
          </p>
        )}
        {subValue && <p className="text-sm text-gray-500">{subValue}</p>}
        {children}
      </div>
      {icon && <div className="rounded-md bg-gray-100 p-3">{icon}</div>}
    </div>
  );
};

export default DashboardStatCard;
