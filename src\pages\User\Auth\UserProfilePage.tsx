import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { UserWrapper } from "@/components/UserWrapper";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { useSDK } from "@/hooks/useSDK";
import { MkdLoader } from "@/components/MkdLoader";
import { DeliveryPartnerModal } from "@/components/DeliveryPartnerModal";

interface IUserProfile {
  email: string;
  first_name: string;
  last_name: string;
  phone?: string | null;
  photo?: string | null;
  address?: string | null;
  city?: string | null;
  state?: string | null;
  zip_code?: string | null;
  country?: string | null;
  member_since?: string | null;
}

interface IPasswordForm {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

const UserProfilePage = () => {
  const { sdk } = useSDK();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<IUserProfile | null>(null);
  const [activeTab, setActiveTab] = useState("personal");
  const [showDeliveryModal, setShowDeliveryModal] = useState(false);
  const [deliveryAgentStatus, setDeliveryAgentStatus] =
    useState("not_registered"); // "not_registered", "pending", "approved", "rejected"

  const profileSchema = yup.object({
    email: yup.string().email("Invalid email").required("Email is required"),
    first_name: yup.string().required("First name is required"),
    last_name: yup.string().required("Last name is required"),
    phone: yup.string().nullable(),
    photo: yup.string().nullable(),
    address: yup.string().nullable(),
    city: yup.string().nullable(),
    state: yup.string().nullable(),
    zip_code: yup.string().nullable(),
    country: yup.string().nullable(),
  });

  const passwordSchema = yup.object({
    current_password: yup.string().required("Current password is required"),
    new_password: yup
      .string()
      .min(6, "Password must be at least 6 characters")
      .required("New password is required"),
    confirm_password: yup
      .string()
      .oneOf([yup.ref("new_password")], "Passwords must match")
      .required("Please confirm your password"),
  });

  const {
    register: registerProfile,
    handleSubmit: handleProfileSubmit,
    setValue,
    formState: { errors: profileErrors },
  } = useForm<IUserProfile>({
    resolver: yupResolver(profileSchema),
  });

  const {
    register: registerPassword,
    handleSubmit: handlePasswordSubmit,
    reset: resetPasswordForm,
    formState: { errors: passwordErrors },
  } = useForm<IPasswordForm>({
    resolver: yupResolver(passwordSchema),
  });

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    setLoading(true);
    try {
      const result = await sdk.getProfile();

      if (result.error) {
        console.error("Error fetching profile:", result.message);
        alert("Failed to load profile. Please try again.");
      } else {
        const profileData = result.model || result.data;
        setProfile(profileData);

        // Set form values
        setValue("email", profileData.email || "");
        setValue("first_name", profileData.first_name || "");
        setValue("last_name", profileData.last_name || "");
        setValue("phone", profileData.phone || "");
        setValue("photo", profileData.photo || "");
        setValue("address", profileData.address || "");
        setValue("city", profileData.city || "");
        setValue("state", profileData.state || "");
        setValue("zip_code", profileData.zip_code || "");
        setValue("country", profileData.country || "");
      }
    } catch (error) {
      console.error("Error fetching profile:", error);
      alert("Failed to load profile. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const onProfileSubmit = async (data: IUserProfile) => {
    setSaving(true);
    try {
      const result = await sdk.updateProfile(data);

      if (result.error) {
        console.error("Error updating profile:", result.message);
        alert("Failed to update profile. Please try again.");
      } else {
        alert("Profile updated successfully!");
        setProfile(data);
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      alert("Failed to update profile. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const onPasswordSubmit = async (data: IPasswordForm) => {
    setSaving(true);
    try {
      // Implement password change logic here
      alert("Password changed successfully!");
      resetPasswordForm();
    } catch (error) {
      console.error("Error changing password:", error);
      alert("Failed to change password. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const handleDeliveryApplicationSubmit = () => {
    // When application is submitted, change status to pending
    setDeliveryAgentStatus("pending");
    setShowDeliveryModal(false);
  };

  if (loading) {
    return (
      <UserWrapper>
        <div className="flex h-full items-center justify-center">
          <MkdLoader />
        </div>
      </UserWrapper>
    );
  }

  return (
    <UserWrapper>
      <div className="h-full bg-[#0F2C59] overflow-auto">
        <div className="p-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-white mb-2">
              Profile Settings
            </h1>
            {/* Temporary test button */}
            <button
              onClick={() =>
                setDeliveryAgentStatus(
                  deliveryAgentStatus === "not_registered"
                    ? "pending"
                    : "not_registered"
                )
              }
              className="text-sm bg-yellow-500 text-black px-3 py-1 rounded mt-2"
            >
              Toggle Status (Test)
            </button>
          </div>

          {/* Personal Information Section */}
          <div className="bg-white rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Personal Information
            </h2>

            <form onSubmit={handleProfileSubmit(onProfileSubmit)}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Profile Photo Section */}
                <div className="md:col-span-2 flex items-start gap-4 mb-6">
                  <div className="flex flex-col items-center">
                    <div className="w-20 h-20 rounded-full bg-gray-300 flex items-center justify-center mb-2">
                      {profile?.photo ? (
                        <img
                          src={profile.photo}
                          alt="Profile"
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        <span className="text-2xl text-gray-600">👤</span>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <button
                        type="button"
                        className="text-sm text-[#0F2C59] hover:underline"
                      >
                        Upload
                      </button>
                      <button
                        type="button"
                        className="text-sm text-gray-500 hover:underline"
                      >
                        Remove
                      </button>
                    </div>
                  </div>
                </div>

                {/* Email Address */}
                <div>
                  <MkdInputV2
                    {...registerProfile("email")}
                    errors={profileErrors.email?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Email Address*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="<EMAIL>"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                  <p className="text-xs text-gray-500 mt-1">
                    We'll always let you know about important changes, but you
                    pick what else you want to hear about.
                  </p>
                </div>

                {/* Location */}
                <div>
                  <MkdInputV2
                    {...registerProfile("city")}
                    errors={profileErrors.city?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Location</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="New York, USA"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>

                {/* Full Name */}
                <div>
                  <MkdInputV2
                    {...registerProfile("first_name")}
                    errors={profileErrors.first_name?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Full Name*</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="Alex Johnson"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>

                {/* Phone Number */}
                <div>
                  <MkdInputV2
                    {...registerProfile("phone")}
                    errors={profileErrors.phone?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Phone Number</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="+****************"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                  <div className="flex items-center mt-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-xs text-gray-500">Verified</span>
                  </div>
                </div>

                {/* Member Since */}
                <div>
                  <MkdInputV2 errors="">
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Member Since</MkdInputV2.Label>
                      <MkdInputV2.Field
                        value="March 15, 2023"
                        disabled
                        className="!border-[#D1D5DB] !bg-gray-100 !text-gray-600"
                      />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>
              </div>

              <div className="flex justify-end mt-6">
                <InteractiveButton
                  type="submit"
                  loading={saving}
                  disabled={saving}
                  className="bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white px-6 py-2 rounded-md"
                >
                  Save Changes
                </InteractiveButton>
              </div>
            </form>
          </div>

          {/* Security Settings Section */}
          <div className="bg-white rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Security Settings
            </h2>

            {/* Password Section */}
            <div className="mb-6">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-base font-medium text-gray-900">
                    Password
                  </h3>
                  <p className="text-sm text-gray-500">
                    Last changed: January 14, 2025
                  </p>
                </div>
                <button
                  type="button"
                  className="text-[#0F2C59] hover:underline text-sm font-medium"
                >
                  Change Password
                </button>
              </div>
            </div>

            {/* Selfie Verification */}
            <div className="mb-6">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-base font-medium text-gray-900">
                    Selfie Verification
                  </h3>
                </div>
                <button
                  type="button"
                  className="text-[#0F2C59] hover:underline text-sm font-medium"
                >
                  Re-upload ID
                </button>
              </div>

              {/* Government ID */}
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-center">
                  <span className="text-red-600 font-medium text-sm mr-2">
                    [Rejected]
                  </span>
                  <span className="text-sm text-gray-700">
                    Error: Image is blurry or unclear.
                  </span>
                </div>
              </div>

              {/* Selfie Verification Status */}
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-green-700 font-medium text-sm mr-2">
                    [Verified]
                  </span>
                  <span className="text-sm text-gray-700">
                    Verified on March 15, 2023
                  </span>
                </div>
              </div>

              {/* Voice Verification */}
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="text-red-600 font-medium text-sm mr-2">
                      [Rejected]
                    </span>
                    <span className="text-sm text-gray-700">
                      Error: Audio quality is poor
                    </span>
                  </div>
                  <button
                    type="button"
                    className="text-[#0F2C59] hover:underline text-sm font-medium"
                  >
                    🎤 Re-record Voice
                  </button>
                </div>
              </div>
            </div>

            {/* Submit for Review Button */}
            <div className="flex justify-center">
              <InteractiveButton
                type="button"
                className="bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white px-8 py-2 rounded-md flex items-center"
              >
                <span className="mr-2">📋</span>
                Submit for Review
              </InteractiveButton>
            </div>

            <p className="text-center text-xs text-gray-500 mt-2">
              Please ensure all documents are clear and well lit before
              submission.
            </p>
          </div>

          {/* Become an eBa Delivery Agent Section */}
          <div className="bg-white rounded-lg p-6">
            {deliveryAgentStatus === "not_registered" ? (
              <>
                <div className="flex items-center mb-4">
                  <span className="text-2xl mr-3">🚚</span>
                  <h2 className="text-xl font-semibold text-gray-900">
                    Become an eBa Delivery Agent
                  </h2>
                </div>

                <p className="text-gray-600 mb-4">
                  Join eBa as a verified delivery partner and earn money helping
                  users receive their purchases safely.
                </p>

                <div className="flex justify-between items-center mb-4">
                  <div>
                    <span className="text-sm font-medium text-gray-700">
                      Status:{" "}
                    </span>
                    <span className="text-sm text-gray-600">
                      Not Registered
                    </span>
                  </div>
                  <InteractiveButton
                    type="button"
                    onClick={() => setShowDeliveryModal(true)}
                    className="bg-[#E63946] hover:bg-[#E63946]/90 text-white px-6 py-2 rounded-md"
                  >
                    Register Now
                  </InteractiveButton>
                </div>

                <p className="text-xs text-gray-500">
                  Complete registration to become a delivery agent
                </p>

                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">
                      By registering as a delivery agent, you agree to our{" "}
                    </span>
                    <div className="flex gap-2">
                      <button className="text-[#E63946] hover:underline text-sm">
                        Delivery Terms and Conditions
                      </button>
                    </div>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <button className="text-[#E63946] hover:underline text-sm">
                      Delete Account
                    </button>
                    <InteractiveButton
                      type="button"
                      className="bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white px-6 py-2 rounded-md"
                    >
                      Save All Changes
                    </InteractiveButton>
                  </div>
                </div>
              </>
            ) : (
              <>
                {/* Pending Application Status */}
                <div className="flex items-center mb-4">
                  <span className="text-2xl mr-3">🚚</span>
                  <h2 className="text-xl font-semibold text-gray-900">
                    Become an eBa Delivery Agent
                  </h2>
                </div>

                <p className="text-gray-600 mb-6">
                  Join eBa as a verified delivery partner and earn while helping
                  users receive their purchases safely.
                </p>

                {/* Status Section */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      Status:
                    </span>
                    <div className="flex items-center">
                      <span className="text-sm text-gray-600 mr-2">
                        Pending Approval
                      </span>
                      <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">ℹ</span>
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-gray-500">
                    Your request is pending admin approval. After verification
                    of your documents, you will see an updated status here.
                  </p>
                </div>

                {/* Verification Items */}
                <div className="space-y-3 mb-6">
                  <div className="flex items-center">
                    <span className="text-lg mr-3">🆔</span>
                    <div className="flex-1">
                      <span className="text-sm font-medium text-gray-700">
                        ID Verification:{" "}
                      </span>
                      <span className="text-sm text-[#0F2C59]">
                        Under Review
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <span className="text-lg mr-3">🚗</span>
                    <div className="flex-1">
                      <span className="text-sm font-medium text-gray-700">
                        Vehicle Documentation:{" "}
                      </span>
                      <span className="text-sm text-[#0F2C59]">
                        Under Review
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <span className="text-lg mr-3">👤</span>
                    <div className="flex-1">
                      <span className="text-sm font-medium text-gray-700">
                        Background Check:{" "}
                      </span>
                      <span className="text-sm text-orange-600">Pending</span>
                    </div>
                  </div>
                </div>

                {/* What happens next */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-[#0F2C59] rounded-full flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-white text-xs">ℹ</span>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-1">
                        What happens next?
                      </h4>
                      <p className="text-sm text-gray-700">
                        Our team is reviewing your application. This process
                        typically takes 2-3 business days. You'll receive an
                        email notification once a decision has been made.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Submission Details */}
                <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
                  <span>Submitted on: April 18, 2025</span>
                  <button className="text-[#0F2C59] hover:underline">
                    View Submission
                  </button>
                </div>

                {/* Terms and Delete Account */}
                <div className="pt-4 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-600">
                      By registering as a delivery agent, you agree to our{" "}
                      <button className="text-[#E63946] hover:underline">
                        Delivery Terms and Conditions
                      </button>
                    </div>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <button className="text-[#E63946] hover:underline text-sm">
                      Delete Account
                    </button>
                    <InteractiveButton
                      type="button"
                      className="bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white px-6 py-2 rounded-md"
                    >
                      Save All Changes
                    </InteractiveButton>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Delivery Partner Modal */}
      <DeliveryPartnerModal
        isOpen={showDeliveryModal}
        onClose={() => setShowDeliveryModal(false)}
        onSubmit={handleDeliveryApplicationSubmit}
      />
    </UserWrapper>
  );
};

export default UserProfilePage;
