import React, { useState } from "react";
import { Modal } from "../Modal";

interface Complaint {
  id: string;
  customerName: string;
  customerAvatar: string;
  date: string;
  rating: number;
  complaintText: string;
  orderId: string;
  type: "seller" | "buyer";
}

interface ComplaintsDashboardModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ComplaintsDashboardModal: React.FC<ComplaintsDashboardModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [activeTab, setActiveTab] = useState<"seller" | "buyer">("seller");
  const [currentPage, setCurrentPage] = useState(1);

  // Mock data based on the image
  const sellerComplaints: Complaint[] = [
    {
      id: "1",
      customerName: "<PERSON>",
      customerAvatar: "/api/placeholder/40/40",
      date: "May 15, 2025",
      rating: 2,
      complaintText:
        "Package was delivered late and the box was damaged. Some items inside were broken due to improper handling. I've requested a refund for the damaged items.",
      orderId: "eBa-36787",
      type: "seller",
    },
    {
      id: "2",
      customerName: "<PERSON>",
      customerAvatar: "/api/placeholder/40/40",
      date: "May 12, 2025",
      rating: 1,
      complaintText:
        "Delivery person was rude and left the package outside in the rain despite clear instructions to place it under the covered area. The contents were partially damaged.",
      orderId: "eBa-45923",
      type: "seller",
    },
    {
      id: "3",
      customerName: "Emma Rodriguez",
      customerAvatar: "/api/placeholder/40/40",
      date: "May 8, 2025",
      rating: 3,
      complaintText:
        "Package was delivered to the wrong address initially. The delivery person corrected the mistake after I called customer service, but it caused a significant delay. The delivery person was apologetic about the situation.",
      orderId: "eBa-38764",
      type: "seller",
    },
  ];

  const buyerComplaints: Complaint[] = [];

  const currentComplaints =
    activeTab === "seller" ? sellerComplaints : buyerComplaints;

  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <svg
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? "text-yellow-400" : "text-gray-300"
            }`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    );
  };

  const handleRespond = (complaintId: string) => {
    console.log("Responding to complaint:", complaintId);
  };

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      title=""
      modalHeader={false}
      classes={{
        modalDialog: "!max-w-2xl !w-full",
        modal: "!bg-black !bg-opacity-50",
        modalContent: "!p-0",
      }}
    >
      <div className="bg-white rounded-lg w-full max-w-2xl mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Complaints Dashboard
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab("seller")}
            className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
              activeTab === "seller"
                ? "text-red-600"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
            style={{
              borderBottomColor:
                activeTab === "seller" ? "#E63946" : "transparent",
            }}
          >
            Seller Complaints
          </button>
          <button
            onClick={() => setActiveTab("buyer")}
            className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
              activeTab === "buyer"
                ? "text-red-600"
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
            style={{
              borderBottomColor:
                activeTab === "buyer" ? "#E63946" : "transparent",
            }}
          >
            Buyer Complaints
          </button>
        </div>

        {/* Content */}
        <div className="p-6 max-h-96 overflow-y-auto">
          {currentComplaints.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No complaints found
            </div>
          ) : (
            <div className="space-y-6">
              {currentComplaints.map((complaint) => (
                <div
                  key={complaint.id}
                  className="border-b border-gray-100 pb-6 last:border-b-0"
                >
                  {/* Customer Info */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-600">
                          {complaint.customerName
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </span>
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">
                          {complaint.customerName}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {complaint.date}
                        </p>
                      </div>
                    </div>
                    {renderStars(complaint.rating)}
                  </div>

                  {/* Complaint Text */}
                  <p className="text-gray-700 text-sm mb-3 leading-relaxed">
                    {complaint.complaintText}
                  </p>

                  {/* Order ID and Respond Button */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      Order #{complaint.orderId}
                    </span>
                    <button
                      onClick={() => handleRespond(complaint.id)}
                      className="text-sm font-medium transition-colors flex items-center"
                      style={{ color: "#0F2C59" }}
                    >
                      📧 Respond
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Pagination */}
        {currentComplaints.length > 0 && (
          <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              ◀ Previous
            </button>

            <div className="flex space-x-1">
              {[1, 2, 3].map((page) => (
                <button
                  key={page}
                  onClick={() => setCurrentPage(page)}
                  className={`w-8 h-8 text-sm rounded ${
                    currentPage === page
                      ? "text-white"
                      : "text-gray-500 hover:text-gray-700"
                  }`}
                  style={{
                    backgroundColor:
                      currentPage === page ? "#0F2C59" : "transparent",
                  }}
                >
                  {page}
                </button>
              ))}
            </div>

            <button
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === 3}
              className="text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next ▶
            </button>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ComplaintsDashboardModal;
