import React from "react";
import { UserWrapper } from "../../../components/UserWrapper";
import { Link } from "react-router-dom";

const UserDashboardPage = () => {
  return (
    <UserWrapper>
      <div className="h-full bg-[#0F2C59] overflow-auto">
        <div className="p-8">
          {/* Welcome Section */}
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-white mb-2">
              Welcome, Alex!
            </h1>
            <p className="text-white text-lg">
              Let's get you started on the eBaDollar Platform
            </p>
          </div>

          {/* Get Started Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white">Get Started</h2>
              <span className="text-white text-lg">2 of 4 completed</span>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="w-full bg-gray-600 rounded-full h-2">
                <div
                  className="bg-[#E63946] h-2 rounded-full"
                  style={{ width: "50%" }}
                ></div>
              </div>
            </div>

            <div className="grid grid-cols-5 gap-4">
              {/* Step 1 - Completed */}
              <div className="bg-white rounded-xl p-6 text-center">
                <div className="w-16 h-16 bg-[#E63946] rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-8 h-8 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <h3 className="font-bold text-[#0F2C59] text-sm">
                  Create Account
                </h3>
              </div>

              {/* Step 2 - Completed */}
              <div className="bg-white rounded-xl p-6 text-center">
                <div className="w-16 h-16 bg-[#E63946] rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-8 h-8 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <h3 className="font-bold text-[#0F2C59] text-sm">
                  Verify Email
                </h3>
              </div>

              {/* Step 3 - Pending */}
              <div className="bg-white rounded-xl p-6 text-center">
                <div className="w-16 h-16 bg-gray-400 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-8 h-8 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="font-bold text-[#0F2C59] text-sm">
                  List First Product
                </h3>
              </div>

              {/* Step 4 - Pending */}
              <div className="bg-white rounded-xl p-6 text-center">
                <div className="w-16 h-16 bg-gray-400 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-8 h-8 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 3H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                  </svg>
                </div>
                <h3 className="font-bold text-[#0F2C59] text-sm">
                  Make First Purchase
                </h3>
              </div>

              {/* Step 5 - Pending */}
              <div className="bg-white rounded-xl p-6 text-center">
                <div className="w-16 h-16 bg-gray-400 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-8 h-8 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <h3 className="font-bold text-[#0F2C59] text-sm">
                  Complete Profile
                </h3>
                <p className="text-gray-500 text-xs mt-1">(Optional)</p>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mb-8">
            <h2 className="text-xl font-bold text-white mb-4">Quick Actions</h2>

            <div className="grid grid-cols-3 gap-4">
              {/* Create a Listing */}
              <div className="bg-white rounded-lg p-6">
                <div className="w-12 h-12 bg-[#E63946] rounded-lg flex items-center justify-center mb-4">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <h3 className="font-bold text-[#0F2C59] mb-2">
                  Create a Listing
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  Add your products and services to sell on eBaDollar and start
                  buying and selling immediately!
                </p>
                <Link
                  to="/user/listings/add"
                  className="inline-block bg-[#E63946] text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-[#d42620] transition-colors"
                >
                  List Now →
                </Link>
              </div>

              {/* View Marketplace */}
              <div className="bg-white rounded-lg p-6">
                <div className="w-12 h-12 bg-[#E63946] rounded-lg flex items-center justify-center mb-4">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                  </svg>
                </div>
                <h3 className="font-bold text-[#0F2C59] mb-2">
                  View Marketplace
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  Browse products and services for sale on the eBaDollar
                  marketplace.
                </p>
                <Link
                  to="/user/marketplace"
                  className="inline-block bg-[#E63946] text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-[#d42620] transition-colors"
                >
                  Shop Now →
                </Link>
              </div>

              {/* Check Rewards */}
              <div className="bg-white rounded-lg p-6">
                <div className="w-12 h-12 bg-[#E63946] rounded-lg flex items-center justify-center mb-4">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <h3 className="font-bold text-[#0F2C59] mb-2">Check Rewards</h3>
                <p className="text-gray-600 text-sm mb-4">
                  View how you can earn rewards and commissions on eBaDollar.
                </p>
                <Link
                  to="/user/rewards"
                  className="inline-block bg-[#E63946] text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-[#d42620] transition-colors"
                >
                  View Rewards →
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UserWrapper>
  );
};

export default UserDashboardPage;
