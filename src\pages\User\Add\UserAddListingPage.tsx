import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { UserWrapper } from "../../../components/UserWrapper";
import InteractiveButton from "../../../components/InteractiveButton/InteractiveButton";
import { MkdInputV2 } from "../../../components/MkdInputV2";
import { useSDK } from "../../../hooks/useSDK";

interface IListingForm {
  // Basic Info
  listingType: string;
  title: string;
  description: string;
  category: string;
  images: File[];

  // Price & Stock
  price: string;
  discountPrice: string;
  quantity: string;

  // Location
  addressLine: string;
  city: string;
  country: string;

  // Shipping
  length: string;
  width: string;
  height: string;
  weight: string;
  fragileHandling: boolean;
  flammableGas: boolean;
  liquidItems: boolean;
  glassBreakable: boolean;
  ebaDelivery: boolean;
  localDelivery: boolean;
  internationalDelivery: boolean;
  pickup: boolean;
  meetup: boolean;
}

const UserAddListingPage = () => {
  const navigate = useNavigate();
  const { sdk } = useSDK();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<IListingForm>({
    // Basic Info
    listingType: "Item",
    title: "3D Camera for Sale",
    description: "",
    category: "",
    images: [],

    // Price & Stock
    price: "0.00",
    discountPrice: "0.00",
    quantity: "1",

    // Location
    addressLine: "123 Main Street, Apt 4B",
    city: "New York",
    country: "United States",

    // Shipping
    length: "0.0",
    width: "0.0",
    height: "0.0",
    weight: "0.0",
    fragileHandling: false,
    flammableGas: false,
    liquidItems: false,
    glassBreakable: false,
    ebaDelivery: false,
    localDelivery: false,
    internationalDelivery: false,
    pickup: false,
    meetup: false,
  });

  const categories = [
    { value: "", label: "Select a category" },
    { value: "electronics", label: "Electronics" },
    { value: "clothing", label: "Clothing" },
    { value: "home", label: "Home & Garden" },
    { value: "sports", label: "Sports" },
    { value: "books", label: "Books" },
    { value: "other", label: "Other" },
  ];

  const steps = [
    { id: 1, title: "Basic Info", subtitle: "" },
    { id: 2, title: "Price & Stock", subtitle: "" },
    { id: 3, title: "Location", subtitle: "" },
    { id: 4, title: "Shipping", subtitle: "" },
    { id: 5, title: "Review", subtitle: "" },
  ];

  const handleInputChange = (
    field: keyof IListingForm,
    value: string | boolean | File[]
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleNext = () => {
    if (currentStep < 5) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    // Validation
    if (!formData.title.trim()) {
      alert("Please enter a listing title");
      return;
    }
    if (!formData.description.trim()) {
      alert("Please enter a description");
      return;
    }
    if (!formData.price.trim() || isNaN(parseFloat(formData.price))) {
      alert("Please enter a valid price");
      return;
    }
    if (!formData.category) {
      alert("Please select a category");
      return;
    }

    setLoading(true);
    try {
      const result = await sdk.callRestAPI(
        {
          name: formData.title.trim(),
          description: formData.description.trim(),
          price: parseFloat(formData.price),
          category: formData.category,
          // Convert images to URLs or handle file upload
          image: null, // Handle image upload separately
          status: "active",
          // user_id would be automatically set by the backend from auth context
        },
        "POST"
      );

      if (result.error) {
        console.error("Error creating listing:", result.message);
        alert("Failed to create listing. Please try again.");
      } else {
        alert("Listing created successfully!");
        navigate("/user/listings");
      }
    } catch (error) {
      console.error("Error creating listing:", error);
      alert("Failed to create listing. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Step indicator component
  const StepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {steps.map((step, index) => (
        <div key={step.id} className="flex items-center">
          <div className="flex flex-col items-center">
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep >= step.id
                  ? "bg-[#E63946] text-white"
                  : currentStep === step.id
                    ? "bg-[#E63946] text-white"
                    : "bg-gray-300 text-gray-600"
              }`}
            >
              {step.id}
            </div>
            <div className="mt-2 text-xs text-center">
              <div className="font-medium text-gray-700">{step.title}</div>
            </div>
          </div>
          {index < steps.length - 1 && (
            <div
              className={`w-16 h-0.5 mx-4 ${
                currentStep > step.id ? "bg-[#E63946]" : "bg-gray-300"
              }`}
            />
          )}
        </div>
      ))}
    </div>
  );

  return (
    <UserWrapper>
      <div className="min-h-screen bg-[#0F2C59]">
        {/* Header */}
        <div className="p-6 pb-0">
          <InteractiveButton
            onClick={() => navigate("/user/listings")}
            className="text-white hover:text-gray-300 mb-4 flex items-center"
          >
            ← Back to Listings
          </InteractiveButton>
          <h1 className="text-2xl font-bold text-white mb-2">
            Create New Listing
          </h1>
        </div>

        {/* Main Content */}
        <div className="p-6">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm">
              {/* Step Indicator */}
              <div className="p-6 border-b border-gray-200">
                <StepIndicator />
              </div>

              {/* Form Content */}
              <div className="p-6">
                {/* Step 1: Basic Info */}
                {currentStep === 1 && (
                  <div className="space-y-6">
                    <h2 className="text-xl font-semibold text-gray-900 mb-6">
                      1. Listing Details
                    </h2>

                    {/* Listing Type */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Listing Type*
                      </label>
                      <p className="text-sm text-gray-500 mb-3">
                        Select whether you're listing an item or service
                      </p>
                      <select
                        className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                        value={formData.listingType}
                        onChange={(e) =>
                          handleInputChange("listingType", e.target.value)
                        }
                      >
                        <option value="Item">Item</option>
                        <option value="Service">Service</option>
                      </select>
                    </div>

                    {/* Title */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Title*(Short name of the item or service required)
                      </label>
                      <MkdInputV2
                        placeholder="3D Camera for Sale"
                        value={formData.title}
                        onChange={(e) =>
                          handleInputChange("title", e.target.value)
                        }
                        required
                      >
                        <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59]" />
                      </MkdInputV2>
                    </div>

                    {/* Description */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Description*(Detailed information about the item
                        required)
                      </label>
                      <textarea
                        className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent resize-none"
                        rows={6}
                        placeholder="Describe your item in detail, including condition, features, etc."
                        value={formData.description}
                        onChange={(e) =>
                          handleInputChange("description", e.target.value)
                        }
                        required
                      />
                    </div>

                    {/* Category */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Category*{" "}
                        <span className="text-blue-600 cursor-pointer">
                          Suggest Category
                        </span>
                      </label>
                      <p className="text-sm text-gray-500 mb-3">
                        Select the most relevant category for your item
                      </p>
                      <select
                        className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                        value={formData.category}
                        onChange={(e) =>
                          handleInputChange("category", e.target.value)
                        }
                        required
                      >
                        {categories.map((cat) => (
                          <option key={cat.value} value={cat.value}>
                            {cat.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Upload Images */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Upload Images*
                      </label>
                      <p className="text-sm text-gray-500 mb-3">
                        Add up to 10 images of your item. First image will be
                        the cover image.
                      </p>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                        <div className="flex flex-col items-center">
                          <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                            <svg
                              className="w-6 h-6 text-gray-400"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                              />
                            </svg>
                          </div>
                          <p className="text-gray-600 mb-2">
                            Drag and drop images here, or click to browse
                          </p>
                          <p className="text-sm text-gray-500">
                            JPG, PNG or GIF, max 5MB each
                          </p>
                          <InteractiveButton
                            type="button"
                            className="mt-4 bg-gray-800 text-white px-4 py-2 rounded-md hover:bg-gray-700"
                          >
                            Upload Images
                          </InteractiveButton>
                        </div>
                      </div>

                      {/* Sample uploaded images - horizontal layout */}
                      <div className="flex gap-3 mt-4 flex-wrap">
                        <div className="relative group">
                          <div className="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden">
                            <img
                              src="https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=100&h=100&fit=crop&crop=center"
                              alt="Uploaded image 1"
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <button className="absolute -top-1 -right-1 w-5 h-5 bg-gray-800 text-white rounded-full text-xs hover:bg-gray-900 flex items-center justify-center">
                            ×
                          </button>
                        </div>
                        <div className="relative group">
                          <div className="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden">
                            <img
                              src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop&crop=center"
                              alt="Uploaded image 2"
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <button className="absolute -top-1 -right-1 w-5 h-5 bg-gray-800 text-white rounded-full text-xs hover:bg-gray-900 flex items-center justify-center">
                            ×
                          </button>
                        </div>
                        <div className="relative group">
                          <div className="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden">
                            <img
                              src="https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=100&h=100&fit=crop&crop=center"
                              alt="Uploaded image 3"
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <button className="absolute -top-1 -right-1 w-5 h-5 bg-gray-800 text-white rounded-full text-xs hover:bg-gray-900 flex items-center justify-center">
                            ×
                          </button>
                        </div>
                        <div className="w-16 h-16 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center text-gray-400 hover:border-gray-400 cursor-pointer">
                          <span className="text-xl">+</span>
                        </div>
                        <div className="w-16 h-16 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center text-gray-400 hover:border-gray-400 cursor-pointer">
                          <span className="text-xl">+</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 2: Pricing */}
                {currentStep === 2 && (
                  <div className="space-y-6">
                    <h2 className="text-xl font-semibold text-gray-900 mb-6">
                      2. Pricing
                    </h2>

                    {/* Price in eBa$ */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Price in eBa$*
                      </label>
                      <p className="text-sm text-gray-500 mb-3">
                        Set the price for your item in eBa currency
                      </p>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                          eBa$
                        </span>
                        <MkdInputV2
                          type="number"
                          placeholder="0.00"
                          value={formData.price}
                          onChange={(e) =>
                            handleInputChange("price", e.target.value)
                          }
                          required
                        >
                          <MkdInputV2.Field
                            className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59] pl-16"
                            step="0.01"
                            min="0"
                          />
                        </MkdInputV2>
                      </div>
                    </div>

                    {/* Optional Discount Price */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Optional Discount Price
                      </label>
                      <p className="text-sm text-gray-500 mb-3">
                        Set a discounted price if you're offering a special deal
                      </p>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                          eBa$
                        </span>
                        <MkdInputV2
                          type="number"
                          placeholder="0.00"
                          value={formData.discountPrice}
                          onChange={(e) =>
                            handleInputChange("discountPrice", e.target.value)
                          }
                        >
                          <MkdInputV2.Field
                            className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59] pl-16"
                            step="0.01"
                            min="0"
                          />
                        </MkdInputV2>
                      </div>
                    </div>

                    {/* Quantity Available */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Quantity Available
                      </label>
                      <p className="text-sm text-gray-500 mb-3">
                        Number of items you have for sale
                      </p>
                      <MkdInputV2
                        type="number"
                        placeholder="1"
                        value={formData.quantity}
                        onChange={(e) =>
                          handleInputChange("quantity", e.target.value)
                        }
                        required
                      >
                        <MkdInputV2.Field
                          className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59]"
                          min="1"
                        />
                      </MkdInputV2>
                    </div>
                  </div>
                )}

                {/* Step 3: Location */}
                {currentStep === 3 && (
                  <div className="space-y-6">
                    <h2 className="text-xl font-semibold text-gray-900 mb-6">
                      3. Location of the Item
                    </h2>
                    <p className="text-sm text-gray-500 mb-6">
                      This is pre-filled from your profile address
                    </p>
                    <div className="flex justify-end mb-4">
                      <button className="text-blue-600 hover:text-blue-800 text-sm">
                        📍 Fill from Location
                      </button>
                    </div>

                    {/* Address Line */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Address Line
                      </label>
                      <MkdInputV2
                        placeholder="123 Main Street, Apt 4B"
                        value={formData.addressLine}
                        onChange={(e) =>
                          handleInputChange("addressLine", e.target.value)
                        }
                        required
                      >
                        <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59]" />
                      </MkdInputV2>
                    </div>

                    {/* City */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        City
                      </label>
                      <MkdInputV2
                        placeholder="New York"
                        value={formData.city}
                        onChange={(e) =>
                          handleInputChange("city", e.target.value)
                        }
                        required
                      >
                        <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59]" />
                      </MkdInputV2>
                    </div>

                    {/* Country */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Country
                      </label>
                      <MkdInputV2
                        placeholder="United States"
                        value={formData.country}
                        onChange={(e) =>
                          handleInputChange("country", e.target.value)
                        }
                        required
                      >
                        <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59]" />
                      </MkdInputV2>
                    </div>
                  </div>
                )}

                {/* Step 4: Shipping Options */}
                {currentStep === 4 && (
                  <div className="space-y-6">
                    <h2 className="text-xl font-semibold text-gray-900 mb-6">
                      4. Shipping Options
                    </h2>

                    {/* Package Dimensions */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Package Dimensions*
                      </label>
                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">
                            Length
                          </label>
                          <div className="flex">
                            <MkdInputV2
                              type="number"
                              placeholder="0.0"
                              value={formData.length}
                              onChange={(e) =>
                                handleInputChange("length", e.target.value)
                              }
                              required
                            >
                              <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59] rounded-r-none" />
                            </MkdInputV2>
                            <select className="border border-l-0 border-gray-300 rounded-r-md px-2 py-2 bg-white text-sm">
                              <option>cm</option>
                              <option>in</option>
                            </select>
                          </div>
                        </div>
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">
                            Width
                          </label>
                          <div className="flex">
                            <MkdInputV2
                              type="number"
                              placeholder="0.0"
                              value={formData.width}
                              onChange={(e) =>
                                handleInputChange("width", e.target.value)
                              }
                              required
                            >
                              <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59] rounded-r-none" />
                            </MkdInputV2>
                            <select className="border border-l-0 border-gray-300 rounded-r-md px-2 py-2 bg-white text-sm">
                              <option>cm</option>
                              <option>in</option>
                            </select>
                          </div>
                        </div>
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">
                            Height
                          </label>
                          <div className="flex">
                            <MkdInputV2
                              type="number"
                              placeholder="0.0"
                              value={formData.height}
                              onChange={(e) =>
                                handleInputChange("height", e.target.value)
                              }
                              required
                            >
                              <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59] rounded-r-none" />
                            </MkdInputV2>
                            <select className="border border-l-0 border-gray-300 rounded-r-md px-2 py-2 bg-white text-sm">
                              <option>cm</option>
                              <option>in</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Package Weight */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Package Weight*
                      </label>
                      <div className="flex max-w-xs">
                        <MkdInputV2
                          type="number"
                          placeholder="0.0"
                          value={formData.weight}
                          onChange={(e) =>
                            handleInputChange("weight", e.target.value)
                          }
                          required
                        >
                          <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0F2C59] rounded-r-none" />
                        </MkdInputV2>
                        <select className="border border-l-0 border-gray-300 rounded-r-md px-2 py-2 bg-white text-sm">
                          <option>kg</option>
                          <option>lb</option>
                        </select>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        = 0.0 kg (0.0 lb)
                      </p>
                    </div>

                    {/* Package Handling Information */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Package Handling Information
                      </label>
                      <div className="space-y-3">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.fragileHandling}
                            onChange={(e) =>
                              handleInputChange(
                                "fragileHandling",
                                e.target.checked
                              )
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="text-sm text-gray-700">
                            Fragile (handle with care)
                          </span>
                          <span className="ml-2 text-gray-400">ℹ️</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.flammableGas}
                            onChange={(e) =>
                              handleInputChange(
                                "flammableGas",
                                e.target.checked
                              )
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="text-sm text-gray-700">
                            Contains Flammable Gas (e.g., aerosols, gas
                            canisters)
                          </span>
                          <span className="ml-2 text-gray-400">ℹ️</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.liquidItems}
                            onChange={(e) =>
                              handleInputChange("liquidItems", e.target.checked)
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="text-sm text-gray-700">
                            Liquid (e.g., bottles, gels, oils)
                          </span>
                          <span className="ml-2 text-gray-400">ℹ️</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.glassBreakable}
                            onChange={(e) =>
                              handleInputChange(
                                "glassBreakable",
                                e.target.checked
                              )
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="text-sm text-gray-700">
                            Glass (breakable material)
                          </span>
                          <span className="ml-2 text-gray-400">ℹ️</span>
                        </label>
                      </div>
                      <p className="text-xs text-gray-500 mt-3">
                        This information is visible to delivery agents to ensure
                        safe and appropriate handling of your item.
                      </p>
                    </div>

                    {/* Choose Delivery Methods */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Choose Delivery Methods*
                      </label>
                      <p className="text-sm text-gray-500 mb-4">
                        Select all applicable shipping methods
                      </p>
                      <div className="space-y-4">
                        <label className="flex items-start">
                          <input
                            type="checkbox"
                            checked={formData.ebaDelivery}
                            onChange={(e) =>
                              handleInputChange("ebaDelivery", e.target.checked)
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                          />
                          <div>
                            <span className="text-sm font-medium text-gray-700">
                              eBa Delivery
                            </span>
                            <p className="text-xs text-gray-500">
                              Pre-paid, platform-managed shipping with QR code
                              scan verification
                            </p>
                          </div>
                        </label>
                        <label className="flex items-start">
                          <input
                            type="checkbox"
                            checked={formData.localDelivery}
                            onChange={(e) =>
                              handleInputChange(
                                "localDelivery",
                                e.target.checked
                              )
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                          />
                          <div>
                            <span className="text-sm font-medium text-gray-700">
                              Local Delivery
                            </span>
                            <p className="text-xs text-gray-500">
                              Same-day delivery arranged by you (car, bike
                              courier, etc.)
                            </p>
                          </div>
                        </label>
                        <label className="flex items-start">
                          <input
                            type="checkbox"
                            checked={formData.internationalDelivery}
                            onChange={(e) =>
                              handleInputChange(
                                "internationalDelivery",
                                e.target.checked
                              )
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                          />
                          <div>
                            <span className="text-sm font-medium text-gray-700">
                              International Delivery
                            </span>
                            <p className="text-xs text-gray-500">
                              Global shipping using FedEx, DHL, UPS, etc.
                            </p>
                          </div>
                        </label>
                        <label className="flex items-start">
                          <input
                            type="checkbox"
                            checked={formData.pickup}
                            onChange={(e) =>
                              handleInputChange("pickup", e.target.checked)
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                          />
                          <div>
                            <span className="text-sm font-medium text-gray-700">
                              Pickup
                            </span>
                            <p className="text-xs text-gray-500">
                              Buyer picks up the item from your location
                            </p>
                          </div>
                        </label>
                        <label className="flex items-start">
                          <input
                            type="checkbox"
                            checked={formData.meetup}
                            onChange={(e) =>
                              handleInputChange("meetup", e.target.checked)
                            }
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                          />
                          <div>
                            <span className="text-sm font-medium text-gray-700">
                              Meet-up
                            </span>
                            <p className="text-xs text-gray-500">
                              Meet at a public place to complete the transaction
                            </p>
                          </div>
                        </label>
                      </div>
                    </div>

                    {/* Listing Expiration */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center text-sm text-gray-600">
                        <span className="mr-2">📅</span>
                        <span>
                          Listing will expire in 30 days (May 22, 2023)
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        You will receive a reminder before expiration. You can
                        reactivate or update later.
                      </p>
                    </div>
                  </div>
                )}

                {/* Step 5: Final Actions */}
                {currentStep === 5 && (
                  <div className="space-y-6">
                    <h2 className="text-xl font-semibold text-gray-900 mb-6">
                      5. Final Actions
                    </h2>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center text-sm text-gray-600 mb-2">
                        <span className="mr-2">ℹ️</span>
                        <span>
                          Please fill in all required fields (marked with *) to
                          create your listing.
                        </span>
                      </div>
                    </div>

                    <div className="flex gap-4">
                      <InteractiveButton
                        type="button"
                        className="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-md hover:bg-gray-50"
                      >
                        💾 Save as Draft
                      </InteractiveButton>
                      <InteractiveButton
                        type="button"
                        className="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-md hover:bg-gray-50"
                      >
                        Cancel
                      </InteractiveButton>
                      <InteractiveButton
                        type="button"
                        onClick={handleSubmit}
                        disabled={loading}
                        className="flex-1 bg-[#E63946] text-white py-3 px-6 rounded-md hover:bg-red-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
                      >
                        {loading ? "Creating..." : "✓ Create Listing"}
                      </InteractiveButton>
                    </div>
                  </div>
                )}

                {/* Navigation Buttons */}
                <div className="flex justify-between pt-8 border-t border-gray-200 mt-8">
                  <InteractiveButton
                    type="button"
                    onClick={handlePrevious}
                    disabled={currentStep === 1}
                    className={`px-6 py-2 rounded-md ${
                      currentStep === 1
                        ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    }`}
                  >
                    Previous
                  </InteractiveButton>

                  {currentStep < 5 && (
                    <InteractiveButton
                      type="button"
                      onClick={handleNext}
                      className="px-6 py-2 bg-[#E63946] text-white rounded-md hover:bg-red-600"
                    >
                      Next
                    </InteractiveButton>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UserWrapper>
  );
};

export default UserAddListingPage;
