import React from "react";
import { UserWrapper } from "../../../components/UserWrapper";
import { InteractiveButton } from "../../../components/InteractiveButton";

const conversations = [
  {
    id: 1,
    name: "TechTreasures",
    message: "Yes, I can offer free shipping if you purchase today.",
    time: "10:24 AM",
    unread: 2,
    avatar: "https://i.pravatar.cc/50?u=techtresures",
    product: "Premium Gaming Laptop",
  },
  {
    id: 2,
    name: "HomeDecorStore",
    message: "Thanks for your interest! The clock is still available.",
    time: "Yesterday",
    unread: 1,
    avatar: "https://i.pravatar.cc/50?u=homedecor",
    product: "Vintage Wall Clock",
  },
  {
    id: 3,
    name: "BookEmporium",
    message:
      "I've shipped your order via EBA delivery. You should receive it by",
    time: "May 18",
    avatar: "https://i.pravatar.cc/50?u=bookemporium",
    product: "Rare Book Collection",
  },
  {
    id: 4,
    name: "<PERSON><PERSON><PERSON>",
    message: "The bike comes with all the accessories shown in the photos.",
    time: "May 15",
    avatar: "https://i.pravatar.cc/50?u=sportsgear",
    product: "Mountain Bike",
  },
  {
    id: 5,
    name: "CameraWorld",
    message:
      "Y es, the warranty is valid internationally. Let me know if you have",
    time: "May 12",
    avatar: "https://i.pravatar.cc/50?u=cameraworld",
    product: "DSLR Camera Bundle",
  },
];

const selectedConversation = {
  product: {
    name: "Premium Gaming Laptop",
    price: "eBa$ 1,299.99",
    image: "https://i.pravatar.cc/100?u=gaminglaptop",
  },
  messages: [
    {
      id: 1,
      sender: "other",
      text: "Hello! Thanks for your interest in the Premium Gaming Laptop. Let me know if you have any questions.",
      time: "May 19, 2025",
      avatar: "https://i.pravatar.cc/50?u=techtresures",
    },
    {
      id: 2,
      sender: "me",
      text: "Hi there! Do you offer free shipping for this laptop?",
      time: "10:18 AM",
    },
    {
      id: 3,
      sender: "other",
      text: "I normally charge $25 for shipping, but I could offer free shipping if you purchase today. Would that work for you?",
      time: "10:24 AM",
      avatar: "https://i.pravatar.cc/50?u=techtresures",
    },
  ],
};

// a helper to get user initials
const getInitials = (name: string) => {
  if (!name) return "";
  const allNames = name.trim().split(" ");
  const initials = allNames
    .slice(0, 2)
    .map((n: string) => n[0])
    .join("")
    .toUpperCase();
  return initials;
};

const UserInboxPage = () => {
  const [selectedConvo, setSelectedConvo] = React.useState(conversations[0]);

  return (
    <UserWrapper>
      <div className="h-full" style={{ backgroundColor: "#0F2C59" }}>
        <div className="flex h-full gap-4 p-4">
          {/* Left Sidebar - Conversations List */}
          <div className="w-[350px] bg-white rounded-lg flex flex-col">
            <div className="p-4 pb-3">
              <h1 className="text-2xl font-bold text-black mb-4">Inbox</h1>
              <div className="flex">
                <button className="bg-[#0F2C59] text-white px-3 py-1 text-sm font-medium">
                  All
                </button>
                <button className="text-gray-600 px-3 py-1 text-sm font-medium hover:bg-gray-50">
                  Unread
                </button>
                <button className="text-gray-600 px-3 py-1 text-sm font-medium hover:bg-gray-50">
                  Archived
                </button>
              </div>
            </div>
            <div className="flex-grow overflow-y-auto">
              {conversations.map((convo) => (
                <div
                  key={convo.id}
                  className={`flex items-start p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50 ${
                    selectedConvo.id === convo.id ? "bg-gray-50" : ""
                  }`}
                  onClick={() => setSelectedConvo(convo)}
                >
                  <div className="w-10 h-10 rounded-full mr-3 bg-gray-300 flex items-center justify-center text-white font-bold text-sm">
                    {getInitials(convo.name)}
                  </div>
                  <div className="flex-grow min-w-0">
                    <div className="flex justify-between items-start mb-1">
                      <h2 className="font-bold text-sm text-black truncate pr-2">
                        {convo.name}
                      </h2>
                      <div className="flex items-center gap-2 flex-shrink-0">
                        <span className="text-xs text-gray-500">
                          {convo.time}
                        </span>
                        {convo.unread && (
                          <span
                            className="text-xs text-white rounded-full h-4 w-4 flex items-center justify-center font-medium"
                            style={{ backgroundColor: "#E63946" }}
                          >
                            {convo.unread}
                          </span>
                        )}
                      </div>
                    </div>
                    <p className="text-xs font-semibold text-gray-700 mb-1 truncate">
                      {convo.product}
                    </p>
                    <p className="text-xs text-gray-600 truncate">
                      {convo.message}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Side - Conversation View */}
          <div className="flex-1 bg-white rounded-lg flex flex-col">
            {/* Header with Product Info */}
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-12 h-12 rounded-md mr-4 bg-gray-300 flex items-center justify-center">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l-1-1m5-5l-1-1"
                    ></path>
                  </svg>
                </div>
                <div>
                  <h2 className="font-bold text-black">
                    {selectedConversation.product.name}
                  </h2>
                  <p className="text-sm text-gray-600">
                    {selectedConversation.product.price}
                  </p>
                </div>
              </div>
              <InteractiveButton className="px-4 !py-2 text-sm text-white rounded-lg bg-[#0F2C59] hover:bg-opacity-90">
                View Listing
              </InteractiveButton>
            </div>

            {/* Messages Area */}
            <div className="flex-grow p-4 overflow-y-auto">
              <div className="text-center my-4">
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                  May 19, 2025
                </span>
              </div>
              {selectedConversation.messages.map((msg) => (
                <div
                  key={msg.id}
                  className={`flex items-end gap-3 my-4 ${msg.sender === "me" ? "justify-end" : "justify-start"}`}
                >
                  {msg.sender === "other" && (
                    <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-white font-bold text-xs">
                      {getInitials("TechTreasures")}
                    </div>
                  )}
                  <div
                    className={`flex flex-col ${msg.sender === "me" ? "items-end" : "items-start"}`}
                  >
                    <div
                      className={`px-4 py-2 rounded-lg max-w-xs lg:max-w-md ${
                        msg.sender === "me"
                          ? "text-white bg-[#0F2C59]"
                          : "bg-gray-200 text-black"
                      }`}
                    >
                      <p className="text-sm">{msg.text}</p>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">{msg.time}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* Message Input Area */}
            <div className="p-4 border-t border-gray-200 flex items-center gap-4">
              <div className="flex-grow flex items-center border border-gray-300 rounded-lg">
                <button className="p-2 text-gray-500 hover:text-gray-700">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                <button className="p-2 text-gray-500 hover:text-gray-700">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 100-2 1 1 0 000 2zm7-1a1 1 0 11-2 0 1 1 0 012 0zm-7.536 5.464a.5.5 0 01.708 0 2.5 2.5 0 003.536 0 .5.5 0 01.708-.708 3.5 3.5 0 01-4.95 0 .5.5 0 010-.708z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                <input
                  type="text"
                  placeholder="Type a message..."
                  className="flex-grow p-2 text-sm bg-transparent focus:outline-none"
                />
              </div>
              <InteractiveButton className="px-6 !py-2 text-sm text-white rounded-lg bg-[#E63946] hover:bg-opacity-90">
                Send
              </InteractiveButton>
            </div>
          </div>
        </div>
      </div>
    </UserWrapper>
  );
};

export default UserInboxPage;
