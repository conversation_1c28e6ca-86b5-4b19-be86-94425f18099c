import React, { useState } from "react";
import { UserWrapper } from "@/components/UserWrapper";

interface IDelivery {
  id: string;
  deliveryId: string;
  pickupFrom: string;
  pickupLocation: string;
  deliverTo: string;
  deliverLocation: string;
  fee: string;
  status: string;
}

const UserMyDeliveriesPage = () => {
  const [activeTab, setActiveTab] = useState("Active Deliveries");

  // Mock data based on the uploaded image
  const mockDeliveries: IDelivery[] = [
    {
      id: "1",
      deliveryId: "DLV-30284",
      pickupFrom: "Sarah's Crafts",
      pickupLocation: "Toronto, ON",
      deliverTo: "<PERSON>",
      deliverLocation: "North York, ON",
      fee: "eBa$ 15.00",
      status: "In Transit",
    },
    {
      id: "2",
      deliveryId: "DLV-30291",
      pickupFrom: "Tech Haven",
      pickupLocation: "Scarborough, ON",
      deliverTo: "Jennifer Lee",
      deliverLocation: "Markham, ON",
      fee: "eBa$ 22.50",
      status: "Picked Up",
    },
    {
      id: "3",
      deliveryId: "DLV-30302",
      pickupFrom: "Green Gardens",
      pickupLocation: "Etobicoke, ON",
      deliverTo: "<PERSON> Wilson",
      deliverLocation: "Mississauga, ON",
      fee: "eBa$ 18.75",
      status: "Assigned",
    },
  ];

  const [selectedDelivery, setSelectedDelivery] = useState<IDelivery | null>(
    mockDeliveries[0]
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "In Transit":
        return "bg-blue-100 text-blue-800";
      case "Picked Up":
        return "bg-green-100 text-green-800";
      case "Assigned":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "In Transit":
        return "🚚";
      case "Picked Up":
        return "✅";
      case "Assigned":
        return "📋";
      default:
        return "📦";
    }
  };

  return (
    <UserWrapper>
      <div className="h-full bg-[#0F2C59] overflow-auto">
        <div className="p-6">
          {/* Header */}
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-white mb-2">
              My Deliveries
            </h1>
          </div>

          {/* Tabs */}
          <div className="mb-6">
            <div className="flex border-b border-gray-600">
              {["Active Deliveries", "Completed Deliveries"].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`px-6 py-3 text-sm font-medium border-b-2 ${
                    activeTab === tab
                      ? "border-[#E63946] text-white"
                      : "border-transparent text-gray-400 hover:text-gray-300"
                  }`}
                >
                  {tab}
                </button>
              ))}
            </div>
          </div>

          {/* Main Content */}
          <div className="space-y-6">
            {/* Deliveries List */}
            <div className="bg-white rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                        Delivery ID
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                        Pickup From
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                        Deliver To
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                        Fee
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                        Status
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {mockDeliveries.map((delivery) => (
                      <tr
                        key={delivery.id}
                        className={`hover:bg-gray-50 cursor-pointer ${
                          selectedDelivery?.id === delivery.id
                            ? "bg-blue-50"
                            : ""
                        }`}
                        onClick={() => setSelectedDelivery(delivery)}
                      >
                        <td className="px-4 py-3 text-sm font-medium text-gray-900">
                          {delivery.deliveryId}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          <div>
                            <div className="font-medium">
                              {delivery.pickupFrom}
                            </div>
                            <div className="text-gray-500 text-xs">
                              {delivery.pickupLocation}
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          <div>
                            <div className="font-medium">
                              {delivery.deliverTo}
                            </div>
                            <div className="text-gray-500 text-xs">
                              {delivery.deliverLocation}
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm font-medium text-gray-900">
                          {delivery.fee}
                        </td>
                        <td className="px-4 py-3 text-sm">
                          <span
                            className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
                              delivery.status
                            )}`}
                          >
                            {delivery.status}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm">
                          <button className="text-gray-400 hover:text-gray-600">
                            👁
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Delivery Details */}
            {selectedDelivery && (
              <div className="bg-[#0F2C59] rounded-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-white">
                    Delivery Details: {selectedDelivery.deliveryId}
                  </h2>
                  <span
                    className={`px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(
                      selectedDelivery.status
                    )}`}
                  >
                    {getStatusIcon(selectedDelivery.status)}{" "}
                    {selectedDelivery.status}
                  </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Left Column */}
                  <div className="space-y-6">
                    {/* Package Information */}
                    <div className="bg-white rounded-lg p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">
                        Package Information
                      </h3>
                      <div className="space-y-3">
                        <div className="flex items-start">
                          <span className="text-gray-600 mr-3 mt-1">📦</span>
                          <div>
                            <div className="font-medium text-gray-900">
                              Small Box
                            </div>
                            <div className="text-sm text-gray-500">
                              Weight: 2.3 kg
                            </div>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <span className="text-gray-600 mr-3 mt-1">🏺</span>
                          <div className="font-medium text-gray-900">
                            Handmade Ceramics
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Pickup Information */}
                    <div className="bg-white rounded-lg p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">
                        Pickup Information
                      </h3>
                      <div className="space-y-3">
                        <div className="flex items-start">
                          <span className="text-gray-600 mr-3 mt-1">🏪</span>
                          <div>
                            <div className="font-medium text-gray-900">
                              {selectedDelivery.pickupFrom}
                            </div>
                            <div className="text-sm text-gray-500">
                              Sarah Johnson
                            </div>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <span className="text-gray-600 mr-3 mt-1">📍</span>
                          <div>
                            <div className="font-medium text-gray-900">
                              123 Maple Street
                            </div>
                            <div className="text-sm text-gray-500">
                              Toronto, ON M4B 1B3
                            </div>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <span className="text-gray-600 mr-3 mt-1">📞</span>
                          <div className="font-medium text-gray-900">
                            (416) 555-1234
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right Column */}
                  <div className="space-y-6">
                    {/* Delivery Information */}
                    <div className="bg-white rounded-lg p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">
                        Delivery Information
                      </h3>
                      <div className="space-y-3">
                        <div className="flex items-start">
                          <span className="text-gray-600 mr-3 mt-1">👤</span>
                          <div>
                            <div className="font-medium text-gray-900">
                              {selectedDelivery.deliverTo}
                            </div>
                            <div className="text-sm text-gray-500">Buyer</div>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <span className="text-gray-600 mr-3 mt-1">📍</span>
                          <div>
                            <div className="font-medium text-gray-900">
                              456 Oak Avenue
                            </div>
                            <div className="text-sm text-gray-500">
                              North York, ON M2N 5P7
                            </div>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <span className="text-gray-600 mr-3 mt-1">📞</span>
                          <div className="font-medium text-gray-900">
                            (416) 555-5678
                          </div>
                        </div>
                        <div className="flex items-start">
                          <span className="text-gray-600 mr-3 mt-1">🕐</span>
                          <div className="font-medium text-gray-900">
                            Deliver by Apr 25, 5:00 PM
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Delivery Log */}
                    <div className="bg-white rounded-lg p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">
                        Delivery Log
                      </h3>
                      <div className="space-y-3">
                        <div className="flex items-start">
                          <span className="text-green-500 mr-3 mt-1">✅</span>
                          <div>
                            <div className="font-medium text-gray-900">
                              Accepted by agent
                            </div>
                            <div className="text-sm text-gray-500">
                              Apr 22, 10:05 AM
                            </div>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <span className="text-green-500 mr-3 mt-1">✅</span>
                          <div>
                            <div className="font-medium text-gray-900">
                              Pickup confirmed
                            </div>
                            <div className="text-sm text-gray-500">
                              Apr 22, 10:48 AM
                            </div>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <span className="text-blue-500 mr-3 mt-1">🚚</span>
                          <div>
                            <div className="font-medium text-gray-900">
                              In transit
                            </div>
                            <div className="text-sm text-gray-500">
                              Apr 22, 11:00 AM
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Confirmation Code Section */}
                <div className="mt-6">
                  <div className="bg-white rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Enter Confirmation Code
                    </h3>
                    <div className="flex gap-3">
                      <input
                        type="text"
                        placeholder="Enter code"
                        className="flex-1 px-4 py-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                      />
                      <button className="px-6 py-3 bg-[#0F2C59] text-white rounded-md text-sm font-medium hover:bg-[#1a3a6b] transition-colors">
                        Update
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </UserWrapper>
  );
};

export default UserMyDeliveriesPage;
