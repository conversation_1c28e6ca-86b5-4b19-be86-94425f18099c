export enum RestAPIMethodEnum {
  GET = "GET",
  POST = "POST",
  PUT = "PUT",
  PUTWHERE = "PUTWHERE",
  DELETE = "DELETE",
  DELETEALL = "DELETEALL",
  GETALL = "GETALL",
  PAGINATE = "PAGINATE",
  CURSORPAGINATE = "CURSORPAGINATE",
  AUTOCOMPLETE = "AUTOCOMPLETE",
}

export enum RoleEnum {
  ADMIN = "admin",
  SUPER_ADMIN = "super_admin",
  USER = "user",
}

export enum ToastStatusEnum {
  SUCCESS = "success",
  ERROR = "error",
  WARNING = "warning",
  INFO = "info",
}
